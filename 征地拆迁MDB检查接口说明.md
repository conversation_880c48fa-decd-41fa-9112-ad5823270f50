# 征地拆迁数据交汇MDB检查接口说明

## 概述

为了支持征地拆迁数据交汇业务流程（LandSurveyProjectFlow），系统新增了专门的MDB检查接口，替代原有的EPS接口。新接口提供了更完善的MDB文件检查功能，包括基础检查、要素类检查、拓扑检查和数据库检查四个步骤。

## 接口配置

### 配置文件修改

在 `Web.config` 和 `Web.Release.config` 中添加了新的配置项：

```xml
<!--MDB检查接口基础地址-->
<add key="MdbCheckApiUrl" value="http://172.16.48.133:5000" />
```

### 代码配置

在 `SystemConfigs.cs` 中添加了新的配置属性：

```csharp
/// <summary>
/// MDB检查接口基础地址
/// </summary>
public static string MdbCheckApiUrl = WebConfigurationManager.AppSettings["MdbCheckApiUrl"];
```

## 接口流程

### 1. 文件上传和检查流程

当业务类型为 `LandSurveyProjectFlow` 时，系统会：

1. **文件上传**：调用 `POST {MdbCheckApiUrl}/mdb/upload` 上传MDB文件
2. **启动检查**：调用 `POST {MdbCheckApiUrl}/mdb/check/{task_id}` 启动检查流程
3. **状态监控**：通过后台任务定期调用 `GET {MdbCheckApiUrl}/mdb/status/{task_id}` 检查状态
4. **结果下载**：检查完成后调用 `GET {MdbCheckApiUrl}/mdb/checkresult/download/{task_id}` 获取结果

### 2. 检查状态说明

- `pending`: 等待中
- `running`: 检查进行中
- `completed`: 检查完成
- `failed`: 检查失败

### 3. 检查步骤

MDB检查包含四个步骤：
- `basic_check`: 基础检查
- `feature_class_check`: 要素类检查
- `topology_check`: 拓扑检查
- `database_check`: 数据库检查

## 代码实现

### 控制器修改

在 `BusinessFlowManageController.PostSurveyProjectResult` 方法中添加了对 `LandSurveyProjectFlow` 的特殊处理：

```csharp
//征地拆迁数据交汇业务 - 使用新的mdb检查接口
else if (record.BusinessClass == nameof(LandSurveyProjectFlow)) {
    // 清空原有数据
    // 调用MDB上传接口
    // 启动MDB检查
    // 安排后台监控任务
}
```

### 后台任务

新增了 `SurveyResultJob.LoadMdbCheckState` 方法，专门用于监控MDB检查状态：

- 自动重试机制：最多20次，每次间隔60秒
- 状态检查：定期查询检查进度
- 结果处理：检查完成后自动下载和解析结果
- 状态更新：更新业务内容信息的检查状态

### 数据模型

使用 `LandSurvey_ContentInfo` 模型存储检查信息：

- `DataCheckID`: 存储任务ID
- `DataCheckState`: 检查状态（0=检查中，1=通过，2=失败）
- `RejectMessage`: 失败原因
- `SurveyMasterName`: 测绘师姓名
- `SurveyMasterNo`: 测绘师编号
- `SurveyMasterSureTime`: 确认时间

## 测试

### 单元测试

在 `SurveyResultJobTests` 中添加了两个测试方法：

1. `TestMdbCheckApiConnectivity`: 测试MDB检查接口连通性
2. `TestMdbCheckResultParsing`: 测试MDB检查结果解析逻辑

### 运行测试

```bash
# 在Visual Studio中运行测试
# 或使用命令行
dotnet test SDCPCWebTests/Jobs/SurveyResultJobTests.cs
```

## 使用说明

### 前端调用

前端仍然调用原有的 `PostSurveyProjectResult` 接口，系统会根据业务类型自动选择使用MDB检查接口还是EPS接口。

### 状态查询

可以通过现有的状态查询接口获取检查进度和结果。

### 错误处理

- 上传失败：返回具体的错误信息
- 检查失败：在 `RejectMessage` 字段中记录详细的失败原因
- 网络异常：自动重试机制确保稳定性

## 注意事项

1. **业务类型判断**：只有 `LandSurveyProjectFlow` 业务类型才会使用新的MDB检查接口
2. **文件格式**：仅支持 `.mdb` 格式的文件
3. **重试机制**：后台任务会自动重试，无需手动干预
4. **状态同步**：检查状态会实时更新到数据库中
5. **向下兼容**：其他业务类型仍使用原有的EPS接口，不受影响

## 故障排查

### 常见问题

1. **接口连接失败**
   - 检查网络连接
   - 确认MDB检查服务是否正常运行
   - 验证配置的URL是否正确

2. **检查一直处于pending状态**
   - 检查MDB检查服务的处理能力
   - 确认文件是否过大
   - 查看服务端日志

3. **检查结果解析失败**
   - 检查返回的JSON格式是否正确
   - 确认所有必要的字段都存在
   - 查看应用程序日志

### 日志查看

系统会在以下位置记录相关日志：
- Hangfire控制台：后台任务执行日志
- 应用程序日志：接口调用和异常信息
- 业务日志：文件上传和处理记录
