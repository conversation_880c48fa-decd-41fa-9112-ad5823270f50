﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Configuration;
using System.Web.WebSockets;
using Antlr.Runtime;
using Hangfire;
using Hangfire.Console;
using Hangfire.Server;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Controllers;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.BusinessContent.GuiHuaDingDian;
using SDCPCWeb.Models.BusinessContent.KanCeDingJie;
using SDCPCWeb.Models.BusinessContent;
using SDCPCWeb.Models.BusinessContent.YanXian;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.System;
using SDCPCWeb.Services;
using SDCPCWeb.Models.BusinessContent.GuiHuaHeShi;
using SDCPCWeb.Models.BusinessContent.ZhengChai;
using SDCPCWeb.Models.EstateConstruction;
using HttpClient = System.Net.Http.HttpClient;
using System.Net.Mail;

namespace SDCPCWeb.Jobs {
    /// <summary>
    /// 测绘成果相关的异步作业
    /// </summary>
    public static class SurveyResultJob {
        private static string ServicesUrl = ExternalApiConfig.EPSSDCServices;
        private static string EPSDownUrl = ExternalApiConfig.EPSDownUrl;
        private static string RootPath = ExternalApiConfig.RootPath;
        private static string xCloudApiUrl = ExternalApiConfig.xCloudApiUrl;
        private static string DataApiUrl = ExternalApiConfig.DataApiUrl;
        private static string MdbCheckApiUrl = ExternalApiConfig.MdbCheckApiUrl;
        private static Random Rand = new Random();
        /// <summary>
        /// 检测测绘成果检查状态
        /// </summary>
        /// <param name="fileid"></param>
        /// <param name="businessID"></param>
        /// <param name="businessType"></param>
        /// <param name="console"></param>
        [DisplayName("检测测绘成果检查状态")]
        [AutomaticRetry(Attempts = 20, DelaysInSeconds = new[] { 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60 })]
        public static void LoadSurveyResultCheckState(string fileid, string businessID, PerformContext console) {
            console?.WriteLine("开始检测测绘成果状态...");

            using (var service = new OracleDataService()) {
                var business = service.GetById<BusinessBaseInfo>(businessID);
                CouncilPlanCheckContentInfo councilPlanCheckContentInfo = null;

                if (business?.BusinessClass == nameof(RealEstatePreSurveyFlow)
                    || business?.BusinessClass == nameof(RealEstatePreCheckSurveyFlow)
                    || business?.BusinessClass == nameof(RealEstatePreSurveyResultChangeFlow)
                    || business?.BusinessClass == nameof(RealEstatePreCheckSurveyAutoFlow)
                    ) {
                    List<PreSurvey_ContentInfo> projectList = service.GetList<PreSurvey_ContentInfo>("DataCheckID='" + fileid + "'");
                    if (!projectList.Any()) {
                        console?.WriteLine("已不存在此FileID，任务结束");
                        return;
                    }
                }
                if (business?.BusinessClass == nameof(RealEstateActualSurveyFlow)
                    || business?.BusinessClass == nameof(RealEstateActualResultChangeFlow)
                    || business?.BusinessClass == nameof(RealEstateOverallActualSurveyFlow)
                    ) {
                    List<EstateActualSurveyContentInfo> projectList = service.GetList<EstateActualSurveyContentInfo>("DataCheckID='" + fileid + "'");
                    if (!projectList.Any()) {
                        console?.WriteLine("已不存在此FileID，任务结束");
                        return;
                    }
                }
                if (business?.BusinessClass == nameof(CouncilPlanCheckFlow)) {
                    List<CouncilPlanCheckContentInfo> projectList = service.GetList<CouncilPlanCheckContentInfo>("DataCheckID='" + fileid + "'");
                    if (!projectList.Any()) {
                        console?.WriteLine("已不存在此FileID，任务结束");
                        return;
                    } else {
                        councilPlanCheckContentInfo = projectList.FirstOrDefault();
                    }
                }

                //调用EPS状态接口 MDBSTATUS 的值
                string GetStateUrl = ServicesUrl + "/GetTableStrInterface";
                GetStateUrl += "?strFileGuid=" + fileid + "";
                string GetStateResponse = HttpGet(GetStateUrl);
                console?.WriteLine("接口GetTableStrInterface返回数据：");
                console?.WriteLine(GetStateResponse);
                System.Xml.XmlDocument StateXml = new System.Xml.XmlDocument();
                StateXml.LoadXml(GetStateResponse);
                JObject StateResult = (JObject)JsonConvert.DeserializeObject(StateXml.InnerText);
                string mdbStatus = StateResult["MDBSTATUS"].Value<string>();
                console?.WriteLine(mdbStatus);

                var state = 0; //检查中进行中;   edit by wy 设置默认值为0
                if (mdbStatus == "mdb数据检查不通过") {
                    state = 2;
                } else if (mdbStatus == "mdb数据检查通过") {
                    state = 1;
                }

                //#warning 测试用，记得修改
#if DEBUG
                //if (business?.BusinessClass == nameof(CouncilPlanCheckFlow))
                //    state = 1;
#endif
                if (state == 0) {
                    console?.WriteLine("检查未完成...1分钟后继续检测");
                    //BackgroundJob.Schedule(() => LoadSurveyResultCheckState(fileid, null), TimeSpan.FromMinutes(1));
                    //console?.WriteLine("已加入计划任务1分钟后继续检查");
                    //return;
                    //抛出异常让作业进入重试，10次未完成，则自动变为失败，需手动重试
                    throw new Exception("检查未完成...1分钟后继续检测");
                }
                if (state == 2) {
                    console?.WriteLine("检查不通过");

                    //清除指定测绘成果
                    var attrs = service.GetList<AttachmentInfo>(
                        $"BusinessID='{businessID}' AND StateCode=0 AND AttachmentCategories IN('{business?.BusinessType}成果','楼盘信息表','竣工规划条件核实信息表','不动产预测绘成果审核反馈','不动产实核测绘成果审核反馈','不动产测绘成果','市政工程规划条件核实成果表')");
                    if (attrs.Any()) {
                        foreach (var attr in attrs) {
                            attr.StateCode = 1;
                            service.UpdateOne(attr);
                        }
                    }
                    console?.WriteLine("已清理自动检测的测绘成果");

                    if (business?.BusinessClass == nameof(RealEstatePreSurveyFlow)
                    || business?.BusinessClass == nameof(RealEstatePreCheckSurveyFlow)
                    || business?.BusinessClass == nameof(RealEstatePreSurveyResultChangeFlow)
                    || business?.BusinessClass == nameof(RealEstatePreCheckSurveyAutoFlow)
                        ) {
                        List<PreSurvey_ContentInfo> projectList = service.GetList<PreSurvey_ContentInfo>("DataCheckID='" + fileid + "'");
                        if (projectList.Any()) {
                            PreSurvey_ContentInfo project = projectList[0];
                            project.DataCheckState = 2;
                            service.UpdateOne(project);
                        }
                    }
                    if (business?.BusinessClass == nameof(RealEstateActualSurveyFlow)
                        || business?.BusinessClass == nameof(RealEstateActualResultChangeFlow)
                        || business?.BusinessClass == nameof(RealEstateOverallActualSurveyFlow)
                        ) {
                        List<EstateActualSurveyContentInfo> projectList = service.GetList<EstateActualSurveyContentInfo>("DataCheckID='" + fileid + "'");
                        if (projectList.Any()) {
                            EstateActualSurveyContentInfo project = projectList[0];
                            project.DataCheckState = 2;
                            service.UpdateOne(project);
                        }
                    }
                    if (business?.BusinessClass == nameof(CouncilPlanCheckFlow)) {
                        List<CouncilPlanCheckContentInfo> projectList = service.GetList<CouncilPlanCheckContentInfo>("DataCheckID='" + fileid + "'");
                        if (projectList.Any()) {
                            CouncilPlanCheckContentInfo project = projectList[0];
                            project.DataCheckState = 2;
                            service.UpdateOne(project);
                        }
                    }
                    return;
                }

                if (state == 1) {
                    console?.WriteLine("检查通过");

                    //清除指定测绘成果
                    var attrs = service.GetList<AttachmentInfo>(
                        $"BusinessID='{businessID}' AND StateCode=0 AND AttachmentCategories IN('{business?.BusinessType}成果','楼盘信息表','竣工规划条件核实信息表','不动产预测绘成果审核反馈','不动产实核测绘成果审核反馈','不动产测绘成果','市政工程规划条件核实成果表')");
                    if (attrs.Any()) {
                        foreach (var attr in attrs) {
                            attr.StateCode = 1;
                            service.UpdateOne(attr);
                        }
                    }

                    console?.WriteLine("已清理自动检测的测绘成果");

                    //根据checkid获取检查的mdb文件，获取生成的楼盘信息表pdf文件，或者项目汇总信息JSON和楼盘信息JSON
                    //mdb文件和pdf文件作为项目成果附件存到附件表和附件路径，项目汇总信息JSON存到ProjectResultInfo字段，楼盘表信息JSON存到BuildingTableInfo字段
                    string GetDataUrl = ServicesUrl + "/GetHjInstanceJson";
                    GetDataUrl += "?strFileGuid=" + fileid + "";
                    string DataResponse = HttpGet(GetDataUrl);
                    System.Xml.XmlDocument DataXml = new System.Xml.XmlDocument();
                    DataXml.LoadXml(DataResponse);
                    JObject DataResult = (JObject)JsonConvert.DeserializeObject(DataXml.InnerText);
                    string BuildingTableInfo = JsonConvert.SerializeObject(DataResult["YUCHLPJSON"]);
                    console?.WriteLine("获取到楼盘信息");
                    console?.WriteLine(BuildingTableInfo);
                    // 共用部分
                    string Year = DateTime.Now.Year.ToString();
                    string Month = DateTime.Now.Month.ToString();
                    string Day = DateTime.Now.Day.ToString();
                    // 获取mdb附件
                    //mdb附件信息
                    AttachmentInfo mdbAttachment = new AttachmentInfo {
                        ID = Guid.NewGuid().ToString("N"),
                        BusinessID = businessID,
                        BusinessType = business?.BusinessType,
                        AttachmentType = "项目成果附件",
                        AttachmentCategories = "不动产测绘成果", //business?.BusinessType + "成果", //2021-06-09 改为固定死的名称，避免修改了流程名称导致前端的附件不显示
                        //LiftTime = DateTime.Now,
                        //ExpirationTime = DateTime.Now.AddYears(10),//获取数据时不支持时间字段为空，默认十年后才过期
                        StateCode = 0
                    };
                    //获取mdb文件  并更新附件记录信息               
                    //mdb文件存储路径
                    string mdbSavePath = Year + "\\" + Month + "\\" + Day + "\\" + businessID;
                    //路径不存在时，自动创建
                    if (!Directory.Exists(RootPath + "\\" + mdbSavePath)) {
                        Directory.CreateDirectory(RootPath + "\\" + mdbSavePath);
                    }

                    mdbSavePath = mdbSavePath + "\\" + mdbAttachment.ID;
                    string mdbGetUrl = EPSDownUrl + "/DownloadFile?strFileGuid=" + fileid + "";
                    string mdbFileName = business?.BusinessType + "成果";//mdbAttachment.AttachmentCategories;
                    HttpWebRequest mdbRequest = (HttpWebRequest)WebRequest.Create(mdbGetUrl);
                    console?.WriteLine("获取mdb成果");
                    HttpWebResponse mdbResponse = mdbRequest.GetResponse() as HttpWebResponse;
                    Stream mdbResponseStream = mdbResponse.GetResponseStream();
                    Stream mdbStream = new FileStream(RootPath + "\\" + mdbSavePath, FileMode.Create);
                    byte[] mdbByte = new byte[1024];
                    int mdbSize = mdbResponseStream.Read(mdbByte, 0, mdbByte.Length);
                    while (mdbSize > 0) {
                        mdbStream.Write(mdbByte, 0, mdbSize);
                        mdbSize = mdbResponseStream.Read(mdbByte, 0, mdbByte.Length);
                    }

                    mdbStream.Close();
                    mdbResponseStream.Close();
                    mdbAttachment.AttachmentName = mdbFileName;
                    mdbAttachment.AttachmentExt = ".mdb";
                    mdbAttachment.AttachmentLength = mdbByte.Length;
                    mdbAttachment.AttachmentPath = mdbSavePath;
                    mdbAttachment.UploadTime = DateTime.Now;
                    service.InsertOne(mdbAttachment);
                    console?.WriteLine("已保存mdb成果附件");

                    //市政工程建设竣工规划核实流程、全面核实业务不需要获取楼盘信息表
                    if (business?.BusinessClass != nameof(CouncilPlanCheckFlow) && business?.BusinessClass != nameof(RealEstateOverallActualSurveyFlow)) {
                        // 获取pdf附件
                        //pdf附件信息
                        AttachmentInfo pdfAttachment = new AttachmentInfo {
                            ID = Guid.NewGuid().ToString("N"),
                            BusinessID = businessID,
                            BusinessType = business?.BusinessType,
                            AttachmentType = "项目成果附件",
                            AttachmentCategories = "楼盘信息表",
                            //LiftTime = DateTime.Now,
                            //ExpirationTime = DateTime.Now.AddYears(10),//获取数据时不支持时间字段为空，默认十年后才过期
                            StateCode = 0
                        };
                        //mdb文件存储路径
                        string pdfSavePath = Year + "\\" + Month + "\\" + Day + "\\" + businessID;
                        //路径不存在时，自动创建
                        if (!Directory.Exists(RootPath + "\\" + pdfSavePath)) {
                            Directory.CreateDirectory(RootPath + "\\" + pdfSavePath);
                        }


                        pdfSavePath = pdfSavePath + "\\" + pdfAttachment.ID;


                        string pdfGetUrl = EPSDownUrl + "/DownloadpdfFile?strFileGuid=" + fileid + "";
                        //#warning 测试用，记得修改
#if DEBUG
                        //pdfGetUrl = "http://localhost:47006/test.pdf";
#endif

                        string pdfFileName = pdfAttachment.AttachmentCategories;
                        HttpWebRequest pdfRequest = (HttpWebRequest)WebRequest.Create(pdfGetUrl);
                        console?.WriteLine("获取楼盘信息表pdf");
                        HttpWebResponse pdfResponse = pdfRequest.GetResponse() as HttpWebResponse;
                        Stream pdfResponseStream = pdfResponse.GetResponseStream();
                        Stream pdfStream = new FileStream(RootPath + "\\" + pdfSavePath, FileMode.Create);
                        byte[] pdfByte = new byte[1024];
                        int pdfSize = pdfResponseStream.Read(pdfByte, 0, pdfByte.Length);
                        while (pdfSize > 0) {
                            pdfStream.Write(pdfByte, 0, pdfSize);
                            pdfSize = pdfResponseStream.Read(pdfByte, 0, pdfByte.Length);
                        }

                        pdfStream.Close();
                        pdfResponseStream.Close();
                        pdfAttachment.AttachmentName = pdfFileName;
                        pdfAttachment.AttachmentExt = ".pdf";
                        pdfAttachment.AttachmentLength = pdfByte.Length;
                        pdfAttachment.AttachmentPath = pdfSavePath;
                        pdfAttachment.UploadTime = DateTime.Now;
                        service.InsertOne(pdfAttachment);
                        console?.WriteLine("已保存楼盘信息表pdf附件");
                    }

                    //市政工程获取市政工程规划条件核实成果表
                    if (business?.BusinessClass == nameof(CouncilPlanCheckFlow)) {
                        // 获取pdf附件
                        //pdf附件信息
                        AttachmentInfo pdfAttachment = new AttachmentInfo {
                            ID = Guid.NewGuid().ToString("N"),
                            BusinessID = businessID,
                            BusinessType = business?.BusinessType,
                            AttachmentType = "项目成果附件",
                            AttachmentCategories = "市政工程规划条件核实成果表",
                            //LiftTime = DateTime.Now,
                            //ExpirationTime = DateTime.Now.AddYears(10),//获取数据时不支持时间字段为空，默认十年后才过期
                            StateCode = 0
                        };
                        //mdb文件存储路径
                        string pdfSavePath = Year + "\\" + Month + "\\" + Day + "\\" + businessID;
                        //路径不存在时，自动创建
                        if (!Directory.Exists(RootPath + "\\" + pdfSavePath)) {
                            Directory.CreateDirectory(RootPath + "\\" + pdfSavePath);
                        }


                        pdfSavePath = pdfSavePath + "\\" + pdfAttachment.ID;


                        string pdfGetUrl = EPSDownUrl + "/DownloadShiZGCPdfFile?strFileGuid=" + fileid;
                        //#warning 测试用，记得修改
#if DEBUG
                        //pdfGetUrl = "http://localhost:47006/test.pdf";
#endif

                        HttpWebRequest pdfRequest = (HttpWebRequest)WebRequest.Create(pdfGetUrl);
                        console?.WriteLine("获取市政工程规划条件核实成果表pdf");
                        HttpWebResponse pdfResponse = pdfRequest.GetResponse() as HttpWebResponse;
                        Stream pdfResponseStream = pdfResponse.GetResponseStream();
                        Stream pdfStream = new FileStream(RootPath + "\\" + pdfSavePath, FileMode.Create);
                        byte[] pdfByte = new byte[1024];
                        int pdfSize = pdfResponseStream.Read(pdfByte, 0, pdfByte.Length);
                        while (pdfSize > 0) {
                            pdfStream.Write(pdfByte, 0, pdfSize);
                            pdfSize = pdfResponseStream.Read(pdfByte, 0, pdfByte.Length);
                        }

                        pdfStream.Close();
                        pdfResponseStream.Close();
                        pdfAttachment.AttachmentName = "竣工规划条件核实及土地核验信息表";
                        pdfAttachment.AttachmentExt = ".pdf";
                        pdfAttachment.AttachmentLength = pdfByte.Length;
                        pdfAttachment.AttachmentPath = pdfSavePath;
                        pdfAttachment.UploadTime = DateTime.Now;
                        service.InsertOne(pdfAttachment);
                        console?.WriteLine("已保存市政工程规划条件核实成果表pdf附件");
                    }

                    if (business?.BusinessClass == BusinessFlowConfig.RealEstatePreSurveyFlow.GetType().Name
                        || business?.BusinessClass == BusinessFlowConfig.RealEstatePreCheckSurveyFlow.GetType().Name
                        || business?.BusinessClass == BusinessFlowConfig.RealEstatePreSurveyResultChangeFlow.GetType().Name
                        || business?.BusinessClass == BusinessFlowConfig.RealEstatePreCheckSurveyAutoFlow.GetType().Name
                    ) {
                        List<PreSurvey_ContentInfo> projectList =
                            service.GetList<PreSurvey_ContentInfo>("DataCheckID='" + fileid + "'");
                        if (projectList.Any()) {
                            PreSurvey_ContentInfo project = projectList[0];
                            project.DataCheckState = 1;
                            project.BuildingTableInfo = BuildingTableInfo;
                            service.UpdateOne(project);
                        }
                    }

                    if (business?.BusinessClass == BusinessFlowConfig.RealEstateActualSurveyFlow.GetType().Name
                        || business?.BusinessClass == nameof(BusinessFlowConfig.RealEstateActualResultChangeFlow)
                    ) {
                        //实核测绘需要多提取竣工规划条件核实信息表
                        AttachmentInfo shchPdfAttachment = new AttachmentInfo {
                            ID = Guid.NewGuid().ToString("N"),
                            BusinessID = businessID,
                            BusinessType = business?.BusinessType,
                            AttachmentType = "项目成果附件",
                            AttachmentCategories = "竣工规划条件核实信息表",
                            //LiftTime = DateTime.Now,
                            //ExpirationTime = DateTime.Now.AddYears(10),//获取数据时不支持时间字段为空，默认十年后才过期
                            StateCode = 0
                        };
                        //附件文件存储路径
                        string shchPdfSavePath = Year + "\\" + Month + "\\" + Day + "\\" + businessID;
                        //路径不存在时，自动创建
                        if (!Directory.Exists(RootPath + "\\" + shchPdfSavePath)) {
                            Directory.CreateDirectory(RootPath + "\\" + shchPdfSavePath);
                        }


                        shchPdfSavePath = shchPdfSavePath + "\\" + shchPdfAttachment.ID;

                        string shchPdfGetUrl = EPSDownUrl + "/DownloadBDCSHPdfFile?strFileGuid=" + fileid + "";
                        //#warning 测试，记得修改
#if DEBUG
                        //shchPdfGetUrl = "http://localhost:47006/test.pdf";
#endif

                        HttpWebRequest getShchPdfRequest = (HttpWebRequest)WebRequest.Create(shchPdfGetUrl);
                        console?.WriteLine("获取竣工规划条件核实信息表pdf");
                        HttpWebResponse shchPdfResponse = getShchPdfRequest.GetResponse() as HttpWebResponse;
                        var contentLength = shchPdfResponse.ContentLength;
                        Stream shchPdfResponseStream = shchPdfResponse.GetResponseStream();
                        Stream shchPdfStream = new FileStream(RootPath + "\\" + shchPdfSavePath, FileMode.Create);
                        byte[] shchPdfByte = new byte[1024];
                        int shchPdfSize = shchPdfResponseStream.Read(shchPdfByte, 0, shchPdfByte.Length);
                        while (shchPdfSize > 0) {
                            shchPdfStream.Write(shchPdfByte, 0, shchPdfSize);
                            shchPdfSize = shchPdfResponseStream.Read(shchPdfByte, 0, shchPdfByte.Length);
                        }

                        shchPdfStream.Close();
                        shchPdfResponseStream.Close();
                        shchPdfAttachment.AttachmentName = "竣工规划条件核实及土地核验信息表";
                        shchPdfAttachment.AttachmentExt = ".pdf";
                        shchPdfAttachment.AttachmentLength = (int)contentLength;
                        shchPdfAttachment.AttachmentPath = shchPdfSavePath;
                        shchPdfAttachment.UploadTime = DateTime.Now;
                        service.InsertOne(shchPdfAttachment);
                        console?.WriteLine("已保存竣工规划条件核实信息表pdf附件");

                        if (business?.BusinessClass == BusinessFlowConfig.RealEstateActualSurveyFlow.GetType().Name
                            || business?.BusinessClass == nameof(BusinessFlowConfig.RealEstateActualResultChangeFlow)
                        ) {
                            List<EstateActualSurveyContentInfo> projectList =
                                service.GetList<EstateActualSurveyContentInfo>("DataCheckID='" + fileid + "'");
                            if (projectList.Any()) {
                                EstateActualSurveyContentInfo project = projectList[0];
                                project.DataCheckState = 1;
                                project.BuildingTableInfo = BuildingTableInfo;
                                service.UpdateOne(project);
                            }
                        }
                    }

                    if (business?.BusinessClass == nameof(BusinessFlowConfig.CouncilPlanCheckFlow)) {
                        List<CouncilPlanCheckContentInfo> projectList =
                            service.GetList<CouncilPlanCheckContentInfo>("DataCheckID='" + fileid + "'");
                        if (projectList.Any()) {
                            CouncilPlanCheckContentInfo project = projectList[0];
                            project.DataCheckState = 1;
                            project.BuildingTableInfo = BuildingTableInfo;
                            service.UpdateOne(project);
                        }
                    }

                    if (business?.BusinessClass == nameof(BusinessFlowConfig.RealEstateOverallActualSurveyFlow)) {
                        //实核测绘需要多提取竣工规划条件核实信息表
                        AttachmentInfo shchPdfAttachment = new AttachmentInfo {
                            ID = Guid.NewGuid().ToString("N"),
                            BusinessID = businessID,
                            BusinessType = business?.BusinessType,
                            AttachmentType = "项目成果附件",
                            AttachmentCategories = "竣工规划条件核实信息表",
                            //LiftTime = DateTime.Now,
                            //ExpirationTime = DateTime.Now.AddYears(10),//获取数据时不支持时间字段为空，默认十年后才过期
                            StateCode = 0
                        };
                        //附件文件存储路径
                        string shchPdfSavePath = Year + "\\" + Month + "\\" + Day + "\\" + businessID;
                        //路径不存在时，自动创建
                        if (!Directory.Exists(RootPath + "\\" + shchPdfSavePath)) {
                            Directory.CreateDirectory(RootPath + "\\" + shchPdfSavePath);
                        }


                        shchPdfSavePath = shchPdfSavePath + "\\" + shchPdfAttachment.ID;

                        string shchPdfGetUrl = EPSDownUrl + "/DownloadEpsFile?strFileGuid=" + fileid + $"&strFilename={HttpUtility.UrlEncode("竣工规划条件全面核实信息表")}&strFileExtension=pdf";
                        //#warning 测试，记得修改
#if DEBUG
                        //shchPdfGetUrl = "http://localhost:47006/test.pdf";
#endif

                        HttpWebRequest getShchPdfRequest = (HttpWebRequest)WebRequest.Create(shchPdfGetUrl);
                        console?.WriteLine("获取竣工规划条件全面核实信息表pdf");
                        HttpWebResponse shchPdfResponse = getShchPdfRequest.GetResponse() as HttpWebResponse;
                        var contentLength = shchPdfResponse.ContentLength;
                        Stream shchPdfResponseStream = shchPdfResponse.GetResponseStream();
                        Stream shchPdfStream = new FileStream(RootPath + "\\" + shchPdfSavePath, FileMode.Create);
                        byte[] shchPdfByte = new byte[1024];
                        int shchPdfSize = shchPdfResponseStream.Read(shchPdfByte, 0, shchPdfByte.Length);
                        while (shchPdfSize > 0) {
                            shchPdfStream.Write(shchPdfByte, 0, shchPdfSize);
                            shchPdfSize = shchPdfResponseStream.Read(shchPdfByte, 0, shchPdfByte.Length);
                        }

                        shchPdfStream.Close();
                        shchPdfResponseStream.Close();
                        shchPdfAttachment.AttachmentName = "竣工规划条件全面核实及土地核验信息表";
                        shchPdfAttachment.AttachmentExt = ".pdf";
                        shchPdfAttachment.AttachmentLength = (int)contentLength;
                        shchPdfAttachment.AttachmentPath = shchPdfSavePath;
                        shchPdfAttachment.UploadTime = DateTime.Now;
                        service.InsertOne(shchPdfAttachment);
                        console?.WriteLine("已保存竣工规划条件全面核实信息表pdf附件");


                        List<EstateActualSurveyContentInfo> projectList =
                            service.GetList<EstateActualSurveyContentInfo>("DataCheckID='" + fileid + "'");
                        if (projectList.Any()) {
                            EstateActualSurveyContentInfo project = projectList[0];
                            project.DataCheckState = 1;
                            project.BuildingTableInfo = BuildingTableInfo;
                            service.UpdateOne(project);
                        }
                    }

                    #region 获取检查报告
                    //暂时只有指定的流程需要自动取检查报告
                    if (business?.BusinessClass == nameof(BusinessFlowConfig.RealEstatePreSurveyResultChangeFlow)
                    || business?.BusinessClass == nameof(BusinessFlowConfig.RealEstatePreCheckSurveyAutoFlow)
                    || business?.BusinessClass == nameof(BusinessFlowConfig.RealEstateActualSurveyFlow)
                    || business?.BusinessClass == nameof(BusinessFlowConfig.RealEstateActualResultChangeFlow)
                    || business?.BusinessClass == nameof(BusinessFlowConfig.RealEstateOverallActualSurveyFlow)) {

                        //清除指定测绘成果
                        attrs = service.GetList<AttachmentInfo>(
                            $"BusinessID='{businessID}' AND StateCode=0 AND AttachmentCategories IN('不动产预核报告','不动产实核报告','全面核实报告')");
                        if (attrs.Any()) {
                            foreach (var attr in attrs) {
                                attr.StateCode = 1;
                                service.UpdateOne(attr);
                            }
                        }

                        string attachmentCategories = "";

                        string getReportPdfUrl = string.Empty;
                        bool? isPreSurvey = null; //是否预测绘
                        if (business?.BusinessClass == BusinessFlowConfig.RealEstatePreSurveyFlow.GetType().Name
                            || business?.BusinessClass == BusinessFlowConfig.RealEstatePreCheckSurveyFlow.GetType().Name
                            || business?.BusinessClass == BusinessFlowConfig.RealEstatePreSurveyBuildingTableChangeFlow.GetType().Name
                            || business?.BusinessClass == BusinessFlowConfig.RealEstatePreSurveyResultChangeFlow.GetType().Name
                            || business?.BusinessClass == BusinessFlowConfig.RealEstatePreCheckSurveyAutoFlow.GetType().Name
                        ) {
                            isPreSurvey = true;
                            getReportPdfUrl = $"{EPSDownUrl}/DownloadYuHPDFFile?strFileGuid={fileid}";
                            attachmentCategories = "不动产预核报告";
                        }

                        if (business?.BusinessClass == BusinessFlowConfig.RealEstateActualSurveyFlow.GetType().Name
                            || business?.BusinessClass == nameof(BusinessFlowConfig.RealEstateActualResultChangeFlow)
                        ) {
                            isPreSurvey = false;
                            getReportPdfUrl = $"{EPSDownUrl}/DownloadShiHPdfFile?strFileGuid={fileid}";
                            attachmentCategories = "不动产实核报告";
                        }

                        if (business?.BusinessClass == nameof(BusinessFlowConfig.RealEstateOverallActualSurveyFlow)) {
                            isPreSurvey = false;
                            getReportPdfUrl = $"{EPSDownUrl}/DownloadEpsFile?strFileGuid={fileid}&strFileName={HttpUtility.UrlEncode("全面核实报告")}&strFileExtension=pdf";
                            attachmentCategories = "全面核实报告";
                        }

                        //#warning 测试用，记得修改
#if DEBUG
                        //getReportPdfUrl = "http://localhost:47006/test.pdf";
#endif
                        console?.WriteLine(getReportPdfUrl);

                        if (isPreSurvey.HasValue) {
                            //检查报告pdf附件信息
                            AttachmentInfo reportPdfAttachment = new AttachmentInfo {
                                ID = Guid.NewGuid().ToString("N"),
                                BusinessID = businessID,
                                BusinessType = business?.BusinessType,
                                AttachmentType = "项目成果附件",
                                AttachmentCategories = attachmentCategories,
                                StateCode = 0
                            };
                            //mdb文件存储路径
                            string reportPdfSavePath = Year + "\\" + Month + "\\" + Day + "\\" + businessID;
                            //路径不存在时，自动创建
                            if (!Directory.Exists(RootPath + "\\" + reportPdfSavePath)) {
                                Directory.CreateDirectory(RootPath + "\\" + reportPdfSavePath);
                            }


                            reportPdfSavePath = reportPdfSavePath + "\\" + reportPdfAttachment.ID;


                            string reportPdfFileName = reportPdfAttachment.AttachmentCategories;
                            HttpWebRequest reportPdfRequest = (HttpWebRequest)WebRequest.Create(getReportPdfUrl);
                            console?.WriteLine("获取检查报告pdf");
                            HttpWebResponse reportPdfResponse = reportPdfRequest.GetResponse() as HttpWebResponse;
                            Stream reportPdfResponseStream = reportPdfResponse.GetResponseStream();
                            using (Stream reportPdfStream = new FileStream(RootPath + "\\" + reportPdfSavePath, FileMode.Create)) {
                                byte[] reportPdfByte = new byte[1024];
                                int reportPdfSize = reportPdfResponseStream.Read(reportPdfByte, 0, reportPdfByte.Length);
                                while (reportPdfSize > 0) {
                                    reportPdfStream.Write(reportPdfByte, 0, reportPdfSize);
                                    reportPdfSize = reportPdfResponseStream.Read(reportPdfByte, 0, reportPdfByte.Length);
                                }

                                reportPdfStream.Close();
                                reportPdfResponseStream.Close();
                                reportPdfAttachment.AttachmentName = reportPdfFileName;
                                reportPdfAttachment.AttachmentExt = ".pdf";
                                reportPdfAttachment.AttachmentLength = reportPdfByte.Length;
                                reportPdfAttachment.AttachmentPath = reportPdfSavePath;
                                reportPdfAttachment.UploadTime = DateTime.Now;
                                service.InsertOne(reportPdfAttachment);
                                console?.WriteLine("已保存检查报告pdf附件");
                            }
                        }
                    }

                    #endregion

                    //edit by wy 2020-08-05 需要后执行更新   否则会出现附件延迟问题
                }
            }
        }

        /// <summary>
        /// 检测EPS下载数据状态
        /// </summary>
        /// <param name="fileid"></param>
        /// <param name="businessID"></param>
        /// <param name="console"></param>
        [DisplayName("检测EPS基础测绘数据提取状态")]
        [AutomaticRetry(Attempts = 20, DelaysInSeconds = new[] { 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60 })]
        public static void LoadEPSDownLoadState(string fileid, string businessID, PerformContext console) {
            console?.WriteLine("开始检测EPS数据提取状态...");
            //调用EPS状态接口 MDBSTATUS 的值
            string GetStateUrl = ServicesUrl + "/GetTableStrInterface";
            GetStateUrl += "?strFileGuid=" + fileid + "";
            string GetStateResponse = HttpGet(GetStateUrl);
            console?.WriteLine("接口GetTableStrInterface返回数据：");
            console?.WriteLine(GetStateResponse);
            System.Xml.XmlDocument StateXml = new System.Xml.XmlDocument();
            StateXml.LoadXml(GetStateResponse);
            JObject StateResult = (JObject)JsonConvert.DeserializeObject(StateXml.InnerText);
            string ProjectAreaStatus = StateResult["PROJECTAREASTATUS"].Value<string>();

            var state = 0; //下载进行中;   edit by wy 设置默认值为0
            if (!ProjectAreaStatus.Contains("下载成功") && ProjectAreaStatus != "" && ProjectAreaStatus != "审核通过") {
                state = 2;//下载失败;
            } else if (ProjectAreaStatus.Contains("下载成功")) {
                state = 1;//下载完成;
            }
            if (state == 0) {
                console?.WriteLine("提取未完成...1分钟后继续检测");
                //抛出异常让作业进入重试，10次未完成，则自动变为失败，需手动重试
                throw new Exception("提取未完成...1分钟后继续检测");
            }

            using (var service = new OracleDataService()) {

                if (state == 2) {
                    console?.WriteLine("提取失败");
                    var business = service.GetById<BusinessBaseInfo>(businessID);
                    //检查不通过，根据fileid获得ContentInfo，回写检查不通过状态，重新上传MDB
                    if (business?.BusinessClass == BusinessFlowConfig.BaseSurveyDataDownloadFlow.GetType().Name) {
                        BaseGISData_ContentInfo project = service.GetById<BaseGISData_ContentInfo>(businessID);
                        project.DownLoadState = 2;
                        service.UpdateOne(project);
                    }

                    if (business?.BusinessClass == BusinessFlowConfig.BlueLineSurveyFlow.GetType().Name) {
                        BlueLineSurveyContentInfo project = service.GetById<BlueLineSurveyContentInfo>(businessID);
                        project.DownLoadState = 2;
                        service.UpdateOne(project);
                    }

                    if (business?.BusinessClass == BusinessFlowConfig.MarkPointSurveyFlow.GetType().Name) {
                        MarkPointSurveyContentInfo project = service.GetById<MarkPointSurveyContentInfo>(businessID);
                        project.DownLoadState = 2;
                        service.UpdateOne(project);
                    }

                    return;
                }

                if (state == 1) {
                    console?.WriteLine("提取成功");
                    console?.WriteLine("正在清理已有的基础数据附件");
                    var oldAttachments = service.GetList<AttachmentInfo>(
                        $"BusinessID='{businessID}' AND (AttachmentType='基础数据' or AttachmentType='项目成果附件')");
                    if (oldAttachments.Any()) {
                        foreach (var item in oldAttachments) {
                            if (item.StateCode != 1) {
                                item.StateCode = 1;
                                service.UpdateOne(item);
                            }
                        }
                    }

                    console?.WriteLine("清理完成");
                    console?.WriteLine("正在保存下载结果信息");

                    var business = service.GetById<BusinessBaseInfo>(businessID);

                    //取本次FileID的提取图层
                    var downloadLayers =
                        ProjectAreaStatus.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
                    string Year = DateTime.Now.Year.ToString();
                    string Month = DateTime.Now.Month.ToString();
                    string Day = DateTime.Now.Day.ToString();
                    // 获取mdb附件，路网数据
                    string ValidateNumber = CreateValidateNumber(4); //提取码
                    if (downloadLayers.Contains("路网数据下载成功")) {
                        console?.WriteLine("正在保存路网数据，mdb格式");

                        AttachmentInfo mdbAttachment = new AttachmentInfo {
                            ID = Guid.NewGuid().ToString("N"),
                            BusinessID = businessID,
                            BusinessType = business?.BusinessType,
                            AttachmentType = "基础数据",
                            AttachmentCategories = "规划路网数据",
                            ExpirationTime = DateTime.Now.AddDays(2), //过期时间
                            ValidateNumber = ValidateNumber, //提取码
                            StateCode = 0
                        };
                        //获取mdb文件  并更新附件记录信息               
                        //mdb文件存储路径
                        string mdbSavePath = Year + "\\" + Month + "\\" + Day + "\\" + businessID;
                        //路径不存在时，自动创建
                        if (!Directory.Exists(RootPath + "\\" + mdbSavePath)) {
                            Directory.CreateDirectory(RootPath + "\\" + mdbSavePath);
                        }

                        mdbSavePath = mdbSavePath + "\\" + mdbAttachment.ID;
                        string mdbGetUrl = EPSDownUrl + "/DownloadMdbFile?strFileGuid=" + fileid + "";
                        string mdbFileName = mdbAttachment.AttachmentCategories;
                        HttpWebRequest mdbRequest = (HttpWebRequest)WebRequest.Create(mdbGetUrl);
                        HttpWebResponse mdbResponse = mdbRequest.GetResponse() as HttpWebResponse;
                        Stream mdbResponseStream = mdbResponse.GetResponseStream();
                        Stream mdbStream = new FileStream(RootPath + "\\" + mdbSavePath, FileMode.Create);
                        byte[] mdbByte = new byte[1024];
                        int mdbSize = mdbResponseStream.Read(mdbByte, 0, mdbByte.Length);
                        while (mdbSize > 0) {
                            mdbStream.Write(mdbByte, 0, mdbSize);
                            mdbSize = mdbResponseStream.Read(mdbByte, 0, mdbByte.Length);
                        }

                        mdbStream.Close();
                        mdbResponseStream.Close();
                        mdbAttachment.AttachmentName = mdbFileName;
                        mdbAttachment.AttachmentExt = ".mdb";
                        mdbAttachment.AttachmentLength = mdbByte.Length;
                        mdbAttachment.AttachmentPath = mdbSavePath;
                        mdbAttachment.UploadTime = DateTime.Now;
                        service.InsertOne(mdbAttachment);
                        console?.WriteLine("路网数据保存完成");

                    }

                    if (downloadLayers.Contains("地形数据下载成功")) {
                        console?.WriteLine("正在保存地形数据，dwg格式");
                        // 获取dwg附件
                        //dwg附件信息
                        AttachmentInfo dwgAttachment = new AttachmentInfo {
                            ID = Guid.NewGuid().ToString("N"),
                            BusinessID = businessID,
                            BusinessType = business?.BusinessType,
                            AttachmentType = "基础数据",
                            AttachmentCategories = "地形数据",
                            ExpirationTime = DateTime.Now.AddDays(2), //过期时间
                            ValidateNumber = ValidateNumber, //提取码
                            StateCode = 0
                        };
                        //获取dwg文件  并更新附件记录信息               
                        //dwg文件存储路径
                        string dwgSavePath = Year + "\\" + Month + "\\" + Day + "\\" + businessID;
                        //路径不存在时，自动创建
                        if (!Directory.Exists(RootPath + "\\" + dwgSavePath)) {
                            Directory.CreateDirectory(RootPath + "\\" + dwgSavePath);
                        }

                        dwgSavePath = dwgSavePath + "\\" + dwgAttachment.ID;
                        string dwgGetUrl = EPSDownUrl + "/DownloadDwgFile?strFileGuid=" + fileid + "";
                        string dwgFileName = dwgAttachment.AttachmentCategories;
                        HttpWebRequest dwgRequest = (HttpWebRequest)WebRequest.Create(dwgGetUrl);
                        HttpWebResponse dwgResponse = dwgRequest.GetResponse() as HttpWebResponse;
                        Stream dwgResponseStream = dwgResponse.GetResponseStream();
                        Stream dwgStream = new FileStream(RootPath + "\\" + dwgSavePath, FileMode.Create);
                        byte[] dwgByte = new byte[1024];
                        int dwgSize = dwgResponseStream.Read(dwgByte, 0, dwgByte.Length);
                        while (dwgSize > 0) {
                            dwgStream.Write(dwgByte, 0, dwgSize);
                            dwgSize = dwgResponseStream.Read(dwgByte, 0, dwgByte.Length);
                        }

                        dwgStream.Close();
                        dwgResponseStream.Close();
                        dwgAttachment.AttachmentName = dwgFileName;
                        dwgAttachment.AttachmentExt = ".dwg";
                        dwgAttachment.AttachmentLength = dwgByte.Length;
                        dwgAttachment.AttachmentPath = dwgSavePath;
                        dwgAttachment.UploadTime = DateTime.Now;
                        service.InsertOne(dwgAttachment);
                        console?.WriteLine("地形数据保存完成");

                    }

                    if (business?.BusinessClass == BusinessFlowConfig.BaseSurveyDataDownloadFlow.GetType().Name &&
                        downloadLayers.Contains("控制性详细规划数据下载成功")) {
                        AttachmentInfo kgAttachment = new AttachmentInfo {
                            ID = Guid.NewGuid().ToString("N"),
                            BusinessID = businessID,
                            BusinessType = business?.BusinessType,
                            AttachmentType = "基础数据",
                            AttachmentCategories = "控制性详细规划基础数据",
                            ExpirationTime = DateTime.Now.AddDays(2), //过期时间
                            ValidateNumber = ValidateNumber, //提取码
                            StateCode = 0
                        };
                        //获取控规文件  并更新附件记录信息               
                        //控规文件存储路径
                        string kgSavePath = Year + "\\" + Month + "\\" + Day + "\\" + businessID;
                        //路径不存在时，自动创建
                        if (!Directory.Exists(RootPath + "\\" + kgSavePath)) {
                            Directory.CreateDirectory(RootPath + "\\" + kgSavePath);
                        }

                        kgSavePath = kgSavePath + "\\" + kgAttachment.ID;
                        string kgGetUrl = EPSDownUrl + "/DownloadKGZipFile?strFileGuid=" + fileid + "";
                        string kgFileName = kgAttachment.AttachmentCategories;
                        HttpWebRequest kgRequest = (HttpWebRequest)WebRequest.Create(kgGetUrl);
                        console?.WriteLine("获取控制性详细规划基础数据");
                        HttpWebResponse kgResponse = kgRequest.GetResponse() as HttpWebResponse;
                        Stream kgResponseStream = kgResponse.GetResponseStream();
                        Stream kgStream = new FileStream(RootPath + "\\" + kgSavePath, FileMode.Create);
                        byte[] kgByte = new byte[1024];
                        int kgSize = kgResponseStream.Read(kgByte, 0, kgByte.Length);
                        while (kgSize > 0) {
                            kgStream.Write(kgByte, 0, kgSize);
                            kgSize = kgResponseStream.Read(kgByte, 0, kgByte.Length);
                        }

                        kgStream.Close();
                        kgResponseStream.Close();
                        kgAttachment.AttachmentName = kgFileName;
                        kgAttachment.AttachmentExt = ".zip";
                        kgAttachment.AttachmentLength = kgByte.Length;
                        kgAttachment.AttachmentPath = kgSavePath;
                        kgAttachment.UploadTime = DateTime.Now;
                        service.InsertOne(kgAttachment);
                        console?.WriteLine("已保存控制性详细规划基础数据");
                    }

                    //edit by wy 2020-08-05 需要后执行更新   否则会出现附件延迟问题
                    if (business?.BusinessClass == BusinessFlowConfig.BaseSurveyDataDownloadFlow.GetType().Name) {
                        BaseGISData_ContentInfo project = service.GetById<BaseGISData_ContentInfo>(businessID);
                        project.DownLoadState = 1;
                        service.UpdateOne(project);
                        console?.WriteLine("开始提交业务");
                        FlowService<BaseSurveyDataDownloadFlow> flow =
                            new FlowService<BaseSurveyDataDownloadFlow>(service);
                        flow.FlowPostToNext(businessID);
                        console?.WriteLine("提交结束");
                    }

                    if (business?.BusinessClass == BusinessFlowConfig.BlueLineSurveyFlow.GetType().Name) {
                        BlueLineSurveyContentInfo project = service.GetById<BlueLineSurveyContentInfo>(businessID);
                        project.DownLoadState = 1;
                        service.UpdateOne(project);
                    }

                    if (business?.BusinessClass == BusinessFlowConfig.MarkPointSurveyFlow.GetType().Name) {
                        MarkPointSurveyContentInfo project = service.GetById<MarkPointSurveyContentInfo>(businessID);
                        project.DownLoadState = 1;
                        service.UpdateOne(project);
                    }
                }
            }
        }

        /// <summary>
        /// 检测汇交成果检查状态
        /// </summary>
        /// <param name="fileid"></param>
        /// <param name="businessID"></param>
        /// <param name="businessType"></param>
        /// <param name="console"></param>
        [DisplayName("检测汇交成果检查状态")]
        [AutomaticRetry(Attempts = 20, DelaysInSeconds = new[] { 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60 })]
        public static void LoadAchievementCheckState(string fileid, string businessID, string businessType, PerformContext console) {
            console?.WriteLine("开始检测汇交成果状态...");
            //调用EPS状态接口 MDBSTATUS 的值
            string GetStateUrl = ServicesUrl + "/GetTableStrInterface";
            GetStateUrl += "?strFileGuid=" + fileid + "";
            string GetStateResponse = HttpGet(GetStateUrl);
            console?.WriteLine("接口GetTableStrInterface返回数据：");
            console?.WriteLine(GetStateResponse);
            System.Xml.XmlDocument StateXml = new System.Xml.XmlDocument();
            StateXml.LoadXml(GetStateResponse);
            JObject StateResult = (JObject)JsonConvert.DeserializeObject(StateXml.InnerText);
            string mdbStatus = StateResult["MDBSTATUS"].Value<string>();

            var state = 0; //检查中进行中;   edit by wy 设置默认值为0
            if (mdbStatus == "mdb数据检查不通过") {
                state = 2;
            } else if (mdbStatus == "mdb数据检查通过") {
                state = 1;
            }
            if (state == 0) {
                console?.WriteLine("检查未完成...1分钟后继续检测");
                throw new Exception("检查未完成...1分钟后继续检测");
            }

            using (var service = new OracleDataService()) {
                if (state == 2) {
                    console?.WriteLine("检查不通过");
                    //检查不通过，根据fileid获得ContentInfo，回写检查不通过状态，重新上传MDB
                    if (businessType == "蓝线图") {
                        BlueLineSurveyContentInfo project = service.GetById<BlueLineSurveyContentInfo>(businessID);
                        project.AchievementCheckState = 2;
                        service.UpdateOne(project);
                    }

                    if (businessType == "拨地定桩") {
                        MarkPointSurveyContentInfo project = service.GetById<MarkPointSurveyContentInfo>(businessID);
                        project.AchievementCheckState = 2;
                        service.UpdateOne(project);
                    }

                    return;
                }

                if (state == 1) {
                    console?.WriteLine("检查通过");
                    // 共用部分
                    string Year = DateTime.Now.Year.ToString();
                    string Month = DateTime.Now.Month.ToString();
                    string Day = DateTime.Now.Day.ToString();
                    // 获取mdb附件
                    //mdb附件信息
                    AttachmentInfo mdbAttachment = new AttachmentInfo {
                        ID = Guid.NewGuid().ToString("N"),
                        BusinessID = businessID,
                        BusinessType = businessType,
                        AttachmentType = "项目成果附件",
                        AttachmentCategories = businessType + "汇交成果",
                        //LiftTime = DateTime.Now,
                        //ExpirationTime = DateTime.Now.AddYears(10),//获取数据时不支持时间字段为空，默认十年后才过期
                        StateCode = 0
                    };
                    //获取mdb文件  并更新附件记录信息               
                    //mdb文件存储路径
                    string mdbSavePath = Year + "\\" + Month + "\\" + Day + "\\" + businessID;
                    //路径不存在时，自动创建
                    if (!Directory.Exists(RootPath + "\\" + mdbSavePath)) {
                        Directory.CreateDirectory(RootPath + "\\" + mdbSavePath);
                    }

                    mdbSavePath = mdbSavePath + "\\" + mdbAttachment.ID;
                    string mdbGetUrl = EPSDownUrl + "/DownloadFile?strFileGuid=" + fileid + "";
                    string mdbFileName = mdbAttachment.AttachmentCategories;
                    HttpWebRequest mdbRequest = (HttpWebRequest)WebRequest.Create(mdbGetUrl);
                    console?.WriteLine("获取mdb成果");
                    HttpWebResponse mdbResponse = mdbRequest.GetResponse() as HttpWebResponse;
                    Stream mdbResponseStream = mdbResponse.GetResponseStream();
                    Stream mdbStream = new FileStream(RootPath + "\\" + mdbSavePath, FileMode.Create);
                    byte[] mdbByte = new byte[1024];
                    int mdbSize = mdbResponseStream.Read(mdbByte, 0, mdbByte.Length);
                    while (mdbSize > 0) {
                        mdbStream.Write(mdbByte, 0, mdbSize);
                        mdbSize = mdbResponseStream.Read(mdbByte, 0, mdbByte.Length);
                    }

                    mdbStream.Close();
                    mdbResponseStream.Close();
                    mdbAttachment.AttachmentName = mdbFileName;
                    mdbAttachment.AttachmentExt = ".mdb";
                    mdbAttachment.AttachmentLength = mdbByte.Length;
                    mdbAttachment.AttachmentPath = mdbSavePath;
                    mdbAttachment.UploadTime = DateTime.Now;
                    service.InsertOne(mdbAttachment);
                    console?.WriteLine("已保存mdb成果附件");
                    //edit by wy 2020-08-05 需要后执行更新   否则会出现附件延迟问题
                    if (businessType == "蓝线图") {
                        BlueLineSurveyContentInfo project = service.GetById<BlueLineSurveyContentInfo>(businessID);
                        project.AchievementCheckState = 1;
                        service.UpdateOne(project);
                    }

                    if (businessType == "拨地定桩") {
                        MarkPointSurveyContentInfo project = service.GetById<MarkPointSurveyContentInfo>(businessID);
                        project.AchievementCheckState = 1;
                        service.UpdateOne(project);
                    }
                }
            }
        }

        /// <summary>
        /// 提取已通过的实核测绘备案凭证PDF到业务附件
        /// </summary>
        /// <param name="realSurveyId"></param>
        /// <param name="console"></param>
        [DisplayName("提取已通过的实核测绘备案凭证PDF到业务附件")]
        [AutomaticRetry(Attempts = 20, DelaysInSeconds = new[] { 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60 })]
        public static async Task SaveRealSurveyAuditResult(string realSurveyId, PerformContext console) {
            //检测是否已经有备案凭证
            console?.WriteLine("检测是否已经有备案凭证");
            using (var service = new OracleDataService()) {
                var attachments = service.GetList<AttachmentInfo>("BusinessId=:id And (AttachmentName='不动产实核业务备案信息表' Or AttachmentName='规划条件全面核实业务备案信息表')", new OracleParameter(":id", OracleDbType.Varchar2) { Value = realSurveyId });
                if (attachments.Any()) {
                    console?.WriteLine("正在清理已保存的备案凭证...");
                    foreach (var attachmentInfo in attachments) {
                        attachmentInfo.StateCode = 1;
                        service.UpdateOne(attachmentInfo);
                    }
                    console?.WriteLine("清理完成");
                } else {
                    console?.WriteLine("没有需要清理的内容");
                }

                var business = service.GetById<BusinessBaseInfo>(realSurveyId);
                if (business == null ||
                    (business.BusinessClass != nameof(RealEstateActualSurveyFlow)
                     && business.BusinessClass != nameof(RealEstateActualResultChangeFlow)
                     && business.BusinessClass != nameof(RealEstateOverallActualSurveyFlow)
                    )) {
                    console?.WriteLine("该业务不是不动产实核测绘业务，无需获取");
                    return;
                }

                //获取附件
                console?.WriteLine("开始获取附件不动产实核业务备案信息表");
                var client = HttpClientFactory.Create();
                var downloadUrl = $"{xCloudApiUrl}/dc/shch/GetBazmPDFbyBH?xsywh={business.BusinessNumber}&xsid=";

                if (business.BusinessClass == nameof(RealEstateActualResultChangeFlow)) {
                    downloadUrl = $"{xCloudApiUrl}/dc/bayx/GetBazmPDFbyBH?xsywh={business.BusinessNumber}&xsid=";
                }
                if (business.BusinessClass == nameof(RealEstateOverallActualSurveyFlow)) {
                    downloadUrl = $"{xCloudApiUrl}/dc/qmch/GetBazmPDFbyBH?xsywh={business.BusinessNumber}&xsid=";
                }

                var response = await client.GetAsync(downloadUrl);
                if (!response.IsSuccessStatusCode) {
                    console?.WriteLine($"获取备案凭证失败，接口[{downloadUrl}]返回状态码是{(int)response.StatusCode}{response.StatusCode}");
                    throw new Exception($"获取备案凭证失败，接口返回状态码是{(int)response.StatusCode}{response.StatusCode}");
                }

                var pdfResponseStream = await response.Content.ReadAsStreamAsync();
                //pdf附件信息
                AttachmentInfo pdfAttachment = new AttachmentInfo {
                    ID = Guid.NewGuid().ToString("N"),
                    BusinessID = business.ID,
                    BusinessType = "不动产实核测绘",
                    AttachmentType = "项目成果附件",
                    AttachmentCategories = "不动产实核测绘成果审核反馈",
                    StateCode = 0
                };
                //文件存储路径
                string Year = DateTime.Now.Year.ToString();
                string Month = DateTime.Now.Month.ToString();
                string Day = DateTime.Now.Day.ToString();
                string pdfSavePath = Year + "\\" + Month + "\\" + Day + "\\" + business.ID;
                //路径不存在时，自动创建
                if (!Directory.Exists(RootPath + "\\" + pdfSavePath)) {
                    Directory.CreateDirectory(RootPath + "\\" + pdfSavePath);
                }
                pdfSavePath = pdfSavePath + "\\" + pdfAttachment.ID;
                string pdfFileName = "不动产实核业务备案信息表";
                if (business.BusinessClass == nameof(RealEstateOverallActualSurveyFlow)) {
                    pdfFileName = "规划条件全面核实业务备案信息表";
                }
                Stream pdfStream = new FileStream(RootPath + "\\" + pdfSavePath, FileMode.Create);
                byte[] pdfByte = new byte[1024];
                int pdfSize = pdfResponseStream.Read(pdfByte, 0, pdfByte.Length);
                while (pdfSize > 0) {
                    pdfStream.Write(pdfByte, 0, pdfSize);
                    pdfSize = pdfResponseStream.Read(pdfByte, 0, pdfByte.Length);
                }
                pdfStream.Close();
                pdfResponseStream.Close();
                pdfAttachment.AttachmentName = pdfFileName;
                pdfAttachment.AttachmentExt = ".pdf";
                pdfAttachment.AttachmentLength = pdfByte.Length;
                pdfAttachment.AttachmentPath = pdfSavePath;
                pdfAttachment.UploadTime = DateTime.Now;
                service.InsertOne(pdfAttachment);
                console?.WriteLine("获取附件备案信息表完成");
            }

        }

        [DisplayName("提取已通过的市政核实业务备案凭证PDF到业务附件")]
        [AutomaticRetry(Attempts = 20, DelaysInSeconds = new[] { 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60 })]
        public static async Task SaveCouncilPlanCheckAuditResult(string surveyId, PerformContext console) {
            //检测是否已经有备案凭证
            console?.WriteLine("检测是否已经有备案凭证");
            using (var service = new OracleDataService()) {
                var attachments = service.GetList<AttachmentInfo>("BusinessId=:id And AttachmentName='市政工程规划条件核实业务备案信息表'", new OracleParameter(":id", OracleDbType.Varchar2) { Value = surveyId });
                if (attachments.Any()) {
                    console?.WriteLine("正在清理已保存的备案凭证...");
                    foreach (var attachmentInfo in attachments) {
                        attachmentInfo.StateCode = 1;
                        service.UpdateOne(attachmentInfo);
                    }
                    console?.WriteLine("清理完成");
                } else {
                    console?.WriteLine("没有需要清理的内容");
                }

                var business = service.GetById<BusinessBaseInfo>(surveyId);
                if (business == null ||
                    (business.BusinessClass != nameof(CouncilPlanCheckFlow)
                    )) {
                    console?.WriteLine("该业务不是市政工程规划条件核实业务，无需获取");
                    return;
                }

                //获取附件
                console?.WriteLine("开始获取附件备案信息表");
                var client = new HttpClient();
                var downloadUrl = $"{xCloudApiUrl}/dc/szgc/GetBazmPDFbyBH?xsywh={business.BusinessNumber}&xsid=";

                var response = await client.GetAsync(downloadUrl);
                if (!response.IsSuccessStatusCode) {
                    console?.WriteLine($"获取备案凭证失败，接口[{downloadUrl}]返回状态码是{(int)response.StatusCode}{response.StatusCode}");
                    throw new Exception($"获取备案凭证失败，接口返回状态码是{(int)response.StatusCode}{response.StatusCode}");
                }

                var pdfResponseStream = await response.Content.ReadAsStreamAsync();
                //pdf附件信息
                AttachmentInfo pdfAttachment = new AttachmentInfo {
                    ID = Guid.NewGuid().ToString("N"),
                    BusinessID = business.ID,
                    BusinessType = "不动产实核测绘",
                    AttachmentType = "项目成果附件",
                    AttachmentCategories = "不动产实核测绘成果审核反馈",
                    StateCode = 0
                };
                //文件存储路径
                string Year = DateTime.Now.Year.ToString();
                string Month = DateTime.Now.Month.ToString();
                string Day = DateTime.Now.Day.ToString();
                string pdfSavePath = Year + "\\" + Month + "\\" + Day + "\\" + business.ID;
                //路径不存在时，自动创建
                if (!Directory.Exists(RootPath + "\\" + pdfSavePath)) {
                    Directory.CreateDirectory(RootPath + "\\" + pdfSavePath);
                }
                pdfSavePath = pdfSavePath + "\\" + pdfAttachment.ID;
                string pdfFileName = "市政工程规划条件核实业务备案信息表";
                Stream pdfStream = new FileStream(RootPath + "\\" + pdfSavePath, FileMode.Create);
                byte[] pdfByte = new byte[1024];
                int pdfSize = pdfResponseStream.Read(pdfByte, 0, pdfByte.Length);
                while (pdfSize > 0) {
                    pdfStream.Write(pdfByte, 0, pdfSize);
                    pdfSize = pdfResponseStream.Read(pdfByte, 0, pdfByte.Length);
                }
                pdfStream.Close();
                pdfResponseStream.Close();
                pdfAttachment.AttachmentName = pdfFileName;
                pdfAttachment.AttachmentExt = ".pdf";
                pdfAttachment.AttachmentLength = pdfByte.Length;
                pdfAttachment.AttachmentPath = pdfSavePath;
                pdfAttachment.UploadTime = DateTime.Now;
                service.InsertOne(pdfAttachment);
                console?.WriteLine("获取附件备案信息表完成");
            }

        }

        /// <summary>
        /// 提取已通过的预核测绘备案凭证PDF到业务附件
        /// </summary>
        /// <param name="preCheckSurveyId"></param>
        /// <param name="console"></param>
        [DisplayName("提取已通过的预核测绘备案凭证PDF到业务附件")]
        [AutomaticRetry(Attempts = 20, DelaysInSeconds = new[] { 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60 })]
        public static async Task SavePreCheckSurveyAuditResult(string preCheckSurveyId, PerformContext console) {
            //检测是否已经有备案凭证
            console?.WriteLine("检测是否已经有备案凭证");
            using (var service = new OracleDataService()) {
                var attachments = service.GetList<AttachmentInfo>("BusinessId=:id And AttachmentName in ('不动产预核业务备案信息表','规划验线备案信息表')", new OracleParameter(":id", OracleDbType.Varchar2) { Value = preCheckSurveyId });
                if (attachments.Any()) {
                    console?.WriteLine("正在清理已保存的备案凭证...");
                    foreach (var attachmentInfo in attachments) {
                        attachmentInfo.StateCode = 1;
                        service.UpdateOne(attachmentInfo);
                    }
                    console?.WriteLine("清理完成");
                } else {
                    console?.WriteLine("没有需要清理的内容");
                }

                var business = service.GetById<BusinessBaseInfo>(preCheckSurveyId);
                if (business == null ||
                    (business.BusinessClass != nameof(RealEstatePreCheckSurveyFlow)
                        && business.BusinessClass != nameof(RealEstatePreCheckSurveyAutoFlow)
                        && business.BusinessClass != nameof(MeasurePutLinePreCheckFlow)
                        && business.BusinessClass != nameof(RealEstatePreSurveyResultChangeFlow)
                        && business.BusinessClass != nameof(CouncilMeasurePutLinePreCheckFlow)
                    )
                    ) {
                    console?.WriteLine("该业务不是不动产预核测绘业务，无需获取");
                    return;
                }

                //获取附件
                console?.WriteLine("开始获取附件不动产预核业务备案信息表");
                var client = new HttpClient();
                var downloadUrl = $"{xCloudApiUrl}/dc/bayx/GetBazmPDFbyBH?xsywh={business.BusinessNumber}&xsid=";

                if (business.BusinessClass == nameof(RealEstatePreCheckSurveyAutoFlow)) {
                    downloadUrl = $"{xCloudApiUrl}/dc/zdyhch/GetBazmPDFbyBH?xsywh={business.BusinessNumber}&rid=";
                }

                if (business.BusinessClass == nameof(MeasurePutLinePreCheckFlow)) {
                    downloadUrl = $"{xCloudApiUrl}/dc/fxgh/GetBazmPDFbyBH?xsywh={business.BusinessNumber}&rid=";
                }

                if (business.BusinessClass == nameof(CouncilMeasurePutLinePreCheckFlow)) {
                    downloadUrl = $"{xCloudApiUrl}/dc/fxgh/GetBazmPDFbyBH?xsywh={business.BusinessNumber}&rid=";
                }

                var response = await client.GetAsync(downloadUrl);
                if (!response.IsSuccessStatusCode) {
                    console?.WriteLine($"获取备案凭证失败，接口[{downloadUrl}]返回状态码是{(int)response.StatusCode}{response.StatusCode}");
                    throw new Exception($"获取备案凭证失败，接口返回状态码是{(int)response.StatusCode}{response.StatusCode}");
                }

                var pdfResponseStream = await response.Content.ReadAsStreamAsync();
                //pdf附件信息
                AttachmentInfo pdfAttachment = new AttachmentInfo {
                    ID = Guid.NewGuid().ToString("N"),
                    BusinessID = business.ID,
                    BusinessType = "不动产预核测绘",
                    AttachmentType = "项目成果附件",
                    AttachmentCategories = "不动产预核测绘成果审核反馈",
                    StateCode = 0
                };
                //文件存储路径
                string Year = DateTime.Now.Year.ToString();
                string Month = DateTime.Now.Month.ToString();
                string Day = DateTime.Now.Day.ToString();
                string pdfSavePath = Year + "\\" + Month + "\\" + Day + "\\" + business.ID;
                //路径不存在时，自动创建
                if (!Directory.Exists(RootPath + "\\" + pdfSavePath)) {
                    Directory.CreateDirectory(RootPath + "\\" + pdfSavePath);
                }
                pdfSavePath = pdfSavePath + "\\" + pdfAttachment.ID;
                string pdfFileName = (business.BusinessClass == nameof(MeasurePutLinePreCheckFlow) || business.BusinessClass == nameof(CouncilMeasurePutLinePreCheckFlow)) ? "规划验线备案信息表" : "不动产预核业务备案信息表";
                Stream pdfStream = new FileStream(RootPath + "\\" + pdfSavePath, FileMode.Create);
                byte[] pdfByte = new byte[1024];
                int pdfSize = pdfResponseStream.Read(pdfByte, 0, pdfByte.Length);
                while (pdfSize > 0) {
                    pdfStream.Write(pdfByte, 0, pdfSize);
                    pdfSize = pdfResponseStream.Read(pdfByte, 0, pdfByte.Length);
                }
                pdfStream.Close();
                pdfResponseStream.Close();
                pdfAttachment.AttachmentName = pdfFileName;
                pdfAttachment.AttachmentExt = ".pdf";
                pdfAttachment.AttachmentLength = pdfByte.Length;
                pdfAttachment.AttachmentPath = pdfSavePath;
                pdfAttachment.UploadTime = DateTime.Now;
                service.InsertOne(pdfAttachment);
                console?.WriteLine("获取附件不动产预核业务备案信息表完成");
            }

        }

        /// <summary>
        /// 提取已通过的实核测绘备案编号
        /// </summary>
        /// <param name="realSurveyId"></param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("提取已通过的实核测绘备案编号")]
        [AutomaticRetry(Attempts = 20, DelaysInSeconds = new[] { 60, 60, 60 })]
        public static async Task SaveRealSurveyAuditResultNo(string realSurveyId, PerformContext console) {
            console?.WriteLine("开始取云平台的不动产实核业务备案编号");
            var httpClient = HttpClientFactory.Create();
            var url = $"{xCloudApiUrl}/dc/shch/GetCode?xsywid={realSurveyId}";
            console?.WriteLine(url);
            var responseMessage = await httpClient.GetAsync(url);
            if (responseMessage.IsSuccessStatusCode) {
                var response = await responseMessage.Content.ReadAsStringAsync();
                console?.WriteLine(response);


                if (string.IsNullOrWhiteSpace(response) || response.Contains("SDC") == false) {
                    throw new Exception(response);
                }

                var sdcno = response.Trim('"');

                console?.WriteLine("完成取云平台的不动产实核业务备案编号");

                //根据实核备案编号获取楼栋和开发商信息
                Hangfire.BackgroundJob.Enqueue(() => SaveZRZInfoBySDCNo(sdcno, realSurveyId, null));
            } else {
                throw new Exception(responseMessage.ReasonPhrase);
            }
        }

        /// <summary>
        /// 根据实核备案编号获取楼栋和开发商信息
        /// </summary>
        /// <param name="sdcno">实核备案编号</param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("根据实核备案编号获取楼栋和开发商信息")]
        [AutomaticRetry(Attempts = 20, DelaysInSeconds = new[] { 60, 60, 60 })]
        public static async Task SaveZRZInfoBySDCNo(string sdcno, string businessId, PerformContext console) {
            console?.WriteLine("开始根据实核备案编号获取楼栋和开发商信息");

            var uri = new Uri(ExternalApiConfig.BDCSystemApiUrl);

            console?.WriteLine("判断开发商是否即核即办的开发商");
            string developerNo = string.Empty;
            using (OracleDataService oracle = new OracleDataService()) {
                var business = oracle.GetById<BusinessBaseInfo>(businessId);
                developerNo = business.DeveloperNo;
            }
            console?.WriteLine($"开发商证件号：{developerNo}");

            if (string.IsNullOrWhiteSpace(developerNo)) {
                console?.WriteLine($"开发商证件号为空，无需继续");
                return;
            }

            var getJHJBUri = new Uri(uri, $"/api/bdcquery/GetJHJBByEnterpriseNo");
            console?.WriteLine(getJHJBUri);

            var httpClient = HttpClientFactory.Create();
            var postData = new { @zjh = developerNo };
            using (StringContent content = new StringContent(JsonConvert.SerializeObject(postData), Encoding.UTF8, "application/json")) {
                var responseMessage = await httpClient.PostAsync(getJHJBUri, content);
                if (responseMessage.IsSuccessStatusCode) {
                    var response = await responseMessage.Content.ReadAsStringAsync();
                    console?.WriteLine(response);

                    JObject jObject = JObject.Parse(response);
                    if (jObject["IsSuccess"].ToObject<bool>() == false) {
                        throw new Exception(jObject["Message"].ToString());
                    }

                    if (jObject["Data"].ToObject<bool>() == false) {
                        console?.WriteLine("该开发商不是即核即办的开发商，不需要继续处理");
                        return;
                    }
                } else {
                    throw new Exception(responseMessage.ReasonPhrase);
                }
            }


            console?.WriteLine("根据备案编号查楼栋信息");
            var getZRZInfoUri = new Uri(uri, $"/api/bdcquery/GetZRZInfoBySDCNo");
            console?.WriteLine(getZRZInfoUri);

            httpClient = HttpClientFactory.Create();
            using (StringContent content = new StringContent(JsonConvert.SerializeObject(new { @sdcno = sdcno, @jhjb = "all" }), Encoding.UTF8, "application/json")) {
                var responseMessage = await httpClient.PostAsync(getZRZInfoUri, content);
                if (responseMessage.IsSuccessStatusCode) {
                    var response = await responseMessage.Content.ReadAsStringAsync();
                    console?.WriteLine(response);

                    JObject jObject = JObject.Parse(response);
                    if (jObject["IsSuccess"].ToObject<bool>() == false) {
                        throw new Exception(jObject["Message"].ToString());
                    }

                    var list = JArray.FromObject(jObject["Data"]);
                    string zrzguid = list[0]["ZRZGUID"].ToString();
                    bool isJHJB = list[0]["DCBZ"].ToString() == "即核即办";
                    if (isJHJB == false) {
                        console?.WriteLine("该楼栋不是即核即办的楼栋，不需要继续处理");
                    } else {
                        using (var service = new OracleDataService()) {
                            var resultInfo = service.GetById<ActualSurveyResultInfo>(zrzguid);
                            bool isAdd = false;
                            if (resultInfo == null) {
                                resultInfo = new ActualSurveyResultInfo() {
                                    ID = zrzguid,
                                    CreateTime = DateTime.Now,
                                    IsOpen = 0
                                };
                                isAdd = true;
                            }
                            resultInfo.BusinessBaseInfoId = businessId;
                            resultInfo.SDCNo = sdcno;
                            resultInfo.ZL = list[0]["ZL"].ToString();
                            resultInfo.DeveloperName = list[0]["DeveloperName"].ToString();
                            resultInfo.DeveloperNo = list[0]["DeveloperNo"].ToString();
                            resultInfo.UpdateTime = DateTime.Now;

                            if (isAdd)
                                service.InsertOne(resultInfo);
                            else
                                service.UpdateOne(resultInfo);


                            console?.WriteLine("完成根据实核备案编号获取楼栋和开发商信息");

                            if (resultInfo.IsOpen == 0 && resultInfo.OriginalDeliveryDate.HasValue == false) {
                                Hangfire.BackgroundJob.Enqueue(() => SaveZRZMiniDeliveryDate(zrzguid, null));
                            }
                        }
                    }

                } else {
                    throw new Exception(responseMessage.ReasonPhrase);
                }
            }
        }

        /// <summary>
        /// 根据楼栋GUID获取最小的网签合同时间
        /// </summary>
        /// <param name="zrzguid"></param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("根据楼栋GUID获取最小的网签合同时间")]
        [AutomaticRetry(Attempts = 3, DelaysInSeconds = new[] { 60, 60, 60 })]
        public static async Task SaveZRZMiniDeliveryDate(string zrzguid, PerformContext console) {
            console?.WriteLine("根据楼栋GUID获取最小的网签合同时间");

            var uri = new Uri(ExternalApiConfig.BDCSystemApiUrl);
            uri = new Uri(uri, $"/api/bdcquery/GetZRZMiniDeliveryDate");
            console?.WriteLine(uri);

            var httpClient = HttpClientFactory.Create();
            var postData = new { zrzguid };
            using (StringContent content = new StringContent(JsonConvert.SerializeObject(postData), Encoding.UTF8, "application/json")) {
                var responseMessage = await httpClient.PostAsync(uri, content);
                if (responseMessage.IsSuccessStatusCode) {
                    var response = await responseMessage.Content.ReadAsStringAsync();
                    console?.WriteLine(response);

                    JObject jObject = JObject.Parse(response);
                    if (jObject["IsSuccess"].ToObject<bool>() == false) {
                        throw new Exception(jObject["Message"].ToString());
                    }

                    string date = jObject["Data"].ToString();

                    using (var service = new OracleDataService()) {
                        var resultInfo = service.GetById<ActualSurveyResultInfo>(zrzguid);
                        if (resultInfo != null) {
                            if (resultInfo.IsOpen == 0 && resultInfo.OriginalDeliveryDate.HasValue == false) {
                                resultInfo.OriginalDeliveryDate = Convert.ToDateTime(date).Date;
                                service.UpdateOne(resultInfo);
                            }
                        } else {
                            throw new Exception("未查询到数据");
                        }
                    }

                } else {
                    throw new Exception(responseMessage.ReasonPhrase);
                }
            }
        }

        /// <summary>
        /// 获取竣工规划条件核实信息表
        /// </summary>
        /// <param name="fileid"></param>
        /// <param name="businessID"></param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("修改竣工规划条件核实信息表备注")]
        [AutomaticRetry(Attempts = 3, DelaysInSeconds = new[] { 60, 60, 60 })]
        public static async Task UpdateJGKHHSBRemark(string remark, string businessID, PerformContext console) {
            console?.WriteLine("开始调用修改竣工规划条件核实信息表接口...");

            remark = (remark ?? "");

            console?.WriteLine("开始清理竣工规划条件核实信息表...");
            BusinessBaseInfo business = null;
            using (var service = new OracleDataService()) {
                //如果有备注，就先删除原来的附件
                //清除指定测绘成果
                var attrs = service.GetList<AttachmentInfo>(
                    $"BusinessID='{businessID}' AND StateCode=0 AND AttachmentCategories IN('竣工规划条件核实信息表')");
                if (attrs.Any()) {
                    foreach (var attr in attrs) {
                        attr.StateCode = 1;
                        service.UpdateOne(attr);
                    }
                }

                business = service.GetById<BusinessBaseInfo>(businessID);
            }

            console?.WriteLine("已清理自竣工规划条件核实信息表");

            var ywmc = "修改竣工规划条件核实信息表";
            if (business.BusinessClass == nameof(RealEstateOverallActualSurveyFlow)) {
                ywmc = "修改全面核实竣工规划信息";
            }
            if (business.BusinessClass == nameof(CouncilPlanCheckFlow)) {
                ywmc = "市政修改竣工规划信息";
            }

            string fileId = string.Empty;

            var servicesUrl = $"{ExternalApiConfig.EPSSDCServices}/ProcessOtherBusiness";
            console?.WriteLine(servicesUrl);
            var httpClient = HttpClientFactory.Create();
            string postData = $"ID={businessID}&strYWMC={HttpUtility.UrlEncode(ywmc)}&strDownloadLayer={HttpUtility.UrlEncode(remark)}&strByte64=&strFileType=";
            using (StringContent content = new StringContent(postData, Encoding.UTF8, "application/x-www-form-urlencoded")) {
                var responseMessage = await httpClient.PostAsync(servicesUrl, content);
                if (responseMessage.IsSuccessStatusCode) {
                    var responseText = await responseMessage.Content.ReadAsStringAsync();
                    console?.WriteLine(responseText);
                    System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
                    xml.LoadXml(responseText);
                    JObject result = JObject.Parse(xml.InnerText);
                    if (result["status"]?.ToString() != "success") {
                        throw new Exception("修改竣工规划条件核实信息表接口返回失败");
                    }

                    fileId = result["FILEID"].ToString();

                    console?.WriteLine("修改成功");

                    Hangfire.BackgroundJob.Enqueue(() => GetJGKHHSB(fileId, businessID, true, null));


                } else {
                    throw new Exception($"修改竣工规划条件核实信息表接口异常，状态码：{(int)responseMessage.StatusCode}");
                }
            }

        }

        /// <summary>
        /// 更新Json数据，并保存楼盘信息
        /// </summary>
        /// <param name="fileid"></param>
        /// <param name="businessID"></param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("更新Json数据，并保存楼盘信息")]
        [AutomaticRetry(Attempts = 3, DelaysInSeconds = new[] { 60, 60, 60 })]
        public static void GetJson(string fileid, string businessID, PerformContext console) {

            console?.WriteLine($"开始通过fileid: {fileid} 获取Json数据");
            string GetDataUrl = $"{ExternalApiConfig.EPSSDCServices}/GetHjInstanceJson";
            GetDataUrl += $"?strFileGuid={fileid}";


            string DataResponse = HttpGet(GetDataUrl);
            System.Xml.XmlDocument DataXml = new System.Xml.XmlDocument();
            DataXml.LoadXml(DataResponse);


            JObject DataResult = JObject.Parse(DataXml.InnerText);
            string BuildingTableInfo = JsonConvert.SerializeObject(DataResult["YUCHLPJSON"]);

            console?.WriteLine($"获取到楼盘信息: {BuildingTableInfo}");

            // 更新数据检查状态和楼盘信息表
            using (var service = new OracleDataService()) {
                List<CouncilPlanCheckContentInfo> projectList =
                    service.GetList<CouncilPlanCheckContentInfo>("ID='" + businessID + "'");
                if (projectList.Any()) {
                    CouncilPlanCheckContentInfo project = projectList[0];
                    project.DataCheckState = -2;
                    project.BuildingTableInfo = BuildingTableInfo;
                    service.UpdateOne(project);
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="businessID"></param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("获取竣工规划条件核实信息表")]
        [AutomaticRetry(Attempts = 20, DelaysInSeconds = new[] { 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60 })]
        public static async Task GetJGKHHSB(string fileId, string businessID, bool isCompleted = true, PerformContext console = null) {

            using (var service = new OracleDataService()) {

                var business = service.GetById<BusinessBaseInfo>(businessID);
                string Attachmentfl = "竣工规划条件核实信息表";
                if (business.BusinessClass == nameof(CouncilPlanCheckFlow)) {
                    Attachmentfl = "市政工程规划条件核实成果表";
                }
                #region 检查PDF文件生成状态
                console?.WriteLine("开始检查PDF文件生成状态");

                //调用EPS状态接口 MDBSTATUS 的值
                string GetStateUrl = ServicesUrl + "/GetTableStrInterface";
                GetStateUrl += "?strFileGuid=" + fileId + "";
                string GetStateResponse = HttpGet(GetStateUrl);
                console?.WriteLine("接口GetTableStrInterface返回数据：");
                console?.WriteLine(GetStateResponse);
                System.Xml.XmlDocument StateXml = new System.Xml.XmlDocument();
                StateXml.LoadXml(GetStateResponse);
                JObject StateResult = (JObject)JsonConvert.DeserializeObject(StateXml.InnerText);
                string mdbStatus = StateResult["MDBSTATUS"].Value<string>();
                console?.WriteLine(mdbStatus);

                var state = 0; //检查中进行中;   edit by wy 设置默认值为0
                if (mdbStatus == "mdb数据检查不通过") {
                    state = 2;
                } else if (mdbStatus == "mdb数据检查通过") {
                    state = 1;
                }

                if (state == 0) {
                    console?.WriteLine("检查未完成...1分钟后继续检测");
                    throw new Exception("检查未完成...1分钟后继续检测");
                }
                if (state == 2) {
                    console?.WriteLine("检查不通过");

                    //清除指定测绘成果
                    var attrs = service.GetList<AttachmentInfo>(
                        $"BusinessID='{businessID}' AND StateCode=0 AND AttachmentCategories IN('{Attachmentfl}')");
                    if (attrs.Any()) {
                        foreach (var attr in attrs) {
                            attr.StateCode = 1;
                            service.UpdateOne(attr);
                        }
                    }

                    console?.WriteLine("已清理自竣工规划条件核实信息表");

                    throw new Exception("检查不通过");
                }

                if (state == 1) {
                    console?.WriteLine("检查通过");
                }
                #endregion

                #region 获取竣工规划条件核实信息表PDF

                if (state == 1) {

                    console?.WriteLine("开始获取竣工规划条件核实信息表...");

                    //清除指定测绘成果
                    var attrs = service.GetList<AttachmentInfo>(
                        $"BusinessID='{businessID}' AND StateCode=0 AND AttachmentCategories IN('{Attachmentfl}')");
                    if (attrs.Any()) {
                        foreach (var attr in attrs) {
                            attr.StateCode = 1;
                            service.UpdateOne(attr);
                        }
                    }

                    console?.WriteLine("已清理自竣工规划条件核实信息表");

                    // 共用部分
                    string Year = DateTime.Now.Year.ToString();
                    string Month = DateTime.Now.Month.ToString();
                    string Day = DateTime.Now.Day.ToString();


                    //实核测绘需要多提取竣工规划条件核实信息表
                    AttachmentInfo shchPdfAttachment = new AttachmentInfo {
                        ID = Guid.NewGuid().ToString("N"),
                        BusinessID = businessID,
                        BusinessType = business?.BusinessType,
                        AttachmentType = "项目成果附件",
                        AttachmentCategories = Attachmentfl,
                        //LiftTime = DateTime.Now,
                        //ExpirationTime = DateTime.Now.AddYears(10),//获取数据时不支持时间字段为空，默认十年后才过期
                        StateCode = 0
                    };
                    //附件文件存储路径
                    string shchPdfSavePath = Year + "\\" + Month + "\\" + Day + "\\" + businessID;
                    //路径不存在时，自动创建
                    if (!Directory.Exists(RootPath + "\\" + shchPdfSavePath)) {
                        Directory.CreateDirectory(RootPath + "\\" + shchPdfSavePath);
                    }


                    shchPdfSavePath = shchPdfSavePath + "\\" + shchPdfAttachment.ID;

                    string shchPdfGetUrl = EPSDownUrl + "/DownloadBDCSHPdfFile?strFileGuid=" + fileId + "";

                    if (business.BusinessClass == nameof(RealEstateOverallActualSurveyFlow)) {
                        shchPdfGetUrl = EPSDownUrl + "/DownloadEpsFile?strFileGuid=" + fileId + $"&strFilename={HttpUtility.UrlEncode("竣工规划条件全面核实信息表")}&strFileExtension=pdf";
                    }

                    if (business.BusinessClass == nameof(CouncilPlanCheckFlow)) {
                        shchPdfGetUrl = EPSDownUrl + "/DownloadEpsFile?strFileGuid=" + fileId + $"&strFilename={HttpUtility.UrlEncode("市政竣工规划条件核实信息表")}&strFileExtension=pdf";
                    }

                    //#warning 测试，记得修改
#if DEBUG
                    //shchPdfGetUrl = "http://localhost:47006/test.pdf";
#endif

                    console.WriteLine(shchPdfGetUrl);
                    HttpWebRequest getShchPdfRequest = (HttpWebRequest)WebRequest.Create(shchPdfGetUrl);
                    console?.WriteLine("获取竣工规划条件核实信息表pdf");
                    HttpWebResponse shchPdfResponse = getShchPdfRequest.GetResponse() as HttpWebResponse;
                    var contentLength = shchPdfResponse.ContentLength;
                    Stream shchPdfResponseStream = shchPdfResponse.GetResponseStream();
                    Stream shchPdfStream = new FileStream(RootPath + "\\" + shchPdfSavePath, FileMode.Create);
                    byte[] shchPdfByte = new byte[1024];
                    int shchPdfSize = shchPdfResponseStream.Read(shchPdfByte, 0, shchPdfByte.Length);
                    while (shchPdfSize > 0) {
                        shchPdfStream.Write(shchPdfByte, 0, shchPdfSize);
                        shchPdfSize = shchPdfResponseStream.Read(shchPdfByte, 0, shchPdfByte.Length);
                    }

                    shchPdfStream.Close();
                    shchPdfResponseStream.Close();
                    shchPdfAttachment.AttachmentName = business.BusinessClass == nameof(RealEstateOverallActualSurveyFlow) ? "竣工规划条件全面核实及土地核验信息表" : "竣工规划条件核实及土地核验信息表";
                    shchPdfAttachment.AttachmentExt = ".pdf";
                    shchPdfAttachment.AttachmentLength = (int)contentLength;
                    shchPdfAttachment.AttachmentPath = shchPdfSavePath;
                    shchPdfAttachment.UploadTime = DateTime.Now;
                    service.InsertOne(shchPdfAttachment);
                    console?.WriteLine("已保存竣工规划条件核实信息表pdf附件");

                    if (isCompleted) {
                        //检查竣工规划条件初步核实证明文件是否已经生成
                        Hangfire.BackgroundJob.Enqueue(() => GetJGKCBHSZM(fileId, businessID, null));
                    }

                }
                #endregion
            }
        }

        /// <summary>
        /// 批量获取竣工规划条件核实信息表
        /// </summary>
        /// <param name="list"></param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("批量获取竣工规划条件核实信息表")]
        public static async Task BatchGetJGKHHSB(JArray list, PerformContext console) {
            foreach (var item in list) {
                string fileId = item["fileId"].ToString();
                string businessID = item["businessID"].ToString();

                console?.WriteLine($"fileId：{fileId}；businessID：{businessID}");
                await SurveyResultJob.GetJGKHHSB(fileId, businessID, true, console);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="businessIds">业务id</param>
        /// <param name="containCompleted">是否包括已办结的业务</param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("批量获取竣工规划条件核实信息表")]
        public static async Task BatchGetJGKHHSB(List<string> businessIds, bool containCompleted = false, PerformContext console = null) {
            using (var service = new OracleDataService()) {
                foreach (var businessID in businessIds) {
                    var business = service.GetById<BusinessBaseInfo>(businessID);
                    if (containCompleted == false && business.StateCode == 2) {
                        console?.WriteLine($"业务已办结，无需处理：{businessID}");
                        continue;
                    }
                    if (business.BusinessClass == nameof(RealEstateActualSurveyFlow) || business.BusinessClass == nameof(RealEstateActualResultChangeFlow)) {
                        var contentInfo = service.GetById<EstateActualSurveyContentInfo>(businessID);
                        console?.WriteLine($"fileId：{contentInfo.DataCheckID}；businessID：{businessID}");
                        if (!string.IsNullOrWhiteSpace(contentInfo.DataCheckID)) {
                            await SurveyResultJob.GetJGKHHSB(contentInfo.DataCheckID, businessID, false, console);
                        }
                        else {
                            console?.WriteLine($"没有文件ID，不需要获取：{businessID}");
                        }
                    }
                    else {
                        console?.WriteLine($"不支持的业务类型：{business.BusinessType}");
                    }
                }
            }
        }

        /// <summary>
        /// 获取竣工规划条件初步核实证明文件
        /// </summary>
        /// <param name="businessID"></param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("检查竣工规划条件初步核实证明文件是否已经生成")]
        [AutomaticRetry(Attempts = 5,
            DelaysInSeconds = new[] { 60, 60, 60, 60, 60 })]
        public static async Task<bool> GetJGKCBHSZM(string fileId, string businessID, PerformContext console) {
            string ywmc = "修改竣工规划核实信息表";

            //判断是否仅实测
            using (var service = new OracleDataService()) {
                var business = service.GetById<BusinessBaseInfo>(businessID);

                //如果不是全面核实业务，就判断是否是仅实测
                if (business.BusinessClass != nameof(RealEstateOverallActualSurveyFlow)) {
                    if (business.BusinessClass != nameof(CouncilPlanCheckFlow)) {
                        var result = await XCloudService.CheckIsOnlySC(businessID, business.BusinessClass);
                        if (result == true) {
                            console?.WriteLine("仅实测无需判断");
                            return true;
                        }
                    }
                } else {
                    //全面核实业务无需要判断
                    console?.WriteLine("全面核实业务无需要判断");
                    return true;

                    //ywmc = "修改全面核实竣工规划信息";
                }


                console?.WriteLine("开始检查PDF文件生成状态");

                //调用EPS状态接口 MDBSTATUS 的值


                string GetStateUrl = ServicesUrl + "/GetTableStrInterface";
                if (business.BusinessClass == nameof(CouncilPlanCheckFlow)) {
                    GetStateUrl += $"?strFileGuid={fileId}";
                } else {
                    GetStateUrl += $"?strFileGuid={businessID}&YWMC={HttpUtility.UrlEncode(ywmc)}";
                }
                string GetStateResponse = HttpGet(GetStateUrl);
                console?.WriteLine("接口GetTableStrInterface返回数据：");
                console?.WriteLine(GetStateResponse);
                System.Xml.XmlDocument StateXml = new System.Xml.XmlDocument();
                StateXml.LoadXml(GetStateResponse);
                JObject StateResult = (JObject)JsonConvert.DeserializeObject(StateXml.InnerText);
                string mdbStatus = StateResult["MDBSTATUS"].Value<string>();
                console?.WriteLine(mdbStatus);

                string statusTxt = "推送文件已生成";

                if (business.BusinessClass == nameof(CouncilPlanCheckFlow)) {
                    statusTxt = "mdb数据检查通过";
                }

                if (mdbStatus.Contains(statusTxt) == false) {
                    console?.WriteLine("检查未完成...1分钟后继续检测");
                    throw new Exception("检查未完成...1分钟后继续检测");
                } else {
                    console?.WriteLine("检查通过");

                    if (business.BusinessClass == nameof(CouncilPlanCheckFlow)) {
                        Hangfire.BackgroundJob.Enqueue(() => GetJson(fileId, businessID, null));
                    }

                    return true;
                }
            }
        }

        /// <summary>
        /// 获取规划条件核实证明文件
        /// </summary>
        /// <param name="id">业务id</param>
        /// <param name="console"></param>
        /// <returns></returns>
        [DisplayName("获取规划条件核实证明文件")]
        [AutomaticRetry(Attempts = 3, DelaysInSeconds = new[] { 60, 60, 60 })]
        public static async Task GetGHHSZMFile(string id, PerformContext console) {

            //检测是否已经有备案凭证
            console?.WriteLine("检测是否已经有规划条件核实证明文件");
            using (var service = new OracleDataService()) {
                var attachments = service.GetList<AttachmentInfo>("BusinessId=:id And AttachmentName='规划条件核实证明'", new OracleParameter(":id", OracleDbType.Varchar2) { Value = id });
                if (attachments.Any()) {
                    console?.WriteLine("正在清理已保存的规划条件核实证明...");
                    foreach (var attachmentInfo in attachments) {
                        attachmentInfo.StateCode = 1;
                        service.UpdateOne(attachmentInfo);
                    }
                    console?.WriteLine("清理完成");
                } else {
                    console?.WriteLine("没有需要清理的内容");
                }

                var business = service.GetById<BusinessBaseInfo>(id);
                if (business == null ||
                    (business.BusinessClass != nameof(RealEstateActualSurveyFlow)
                    )) {
                    console?.WriteLine("该业务不是不动产实核测绘业务，无需获取");
                    return;
                }

                //获取附件
                console?.WriteLine("开始获取附件规划条件核实证明文件");

                var result = await XCloudService.GetGHHSZM(id, console);
                var pdfResponseStream = result.Item1;

                //pdf附件信息
                AttachmentInfo pdfAttachment = new AttachmentInfo {
                    ID = Guid.NewGuid().ToString("N"),
                    BusinessID = business.ID,
                    BusinessType = "不动产实核测绘",
                    AttachmentType = "项目成果附件",
                    AttachmentCategories = "规划条件核实证明",
                    StateCode = 0
                };
                //文件存储路径
                string Year = DateTime.Now.Year.ToString();
                string Month = DateTime.Now.Month.ToString();
                string Day = DateTime.Now.Day.ToString();
                string pdfSavePath = Year + "\\" + Month + "\\" + Day + "\\" + business.ID;
                //路径不存在时，自动创建
                if (!Directory.Exists(RootPath + "\\" + pdfSavePath)) {
                    Directory.CreateDirectory(RootPath + "\\" + pdfSavePath);
                }
                pdfSavePath = pdfSavePath + "\\" + pdfAttachment.ID;
                string pdfFileName = "规划条件核实证明";
                Stream pdfStream = new FileStream(RootPath + "\\" + pdfSavePath, FileMode.Create);
                byte[] pdfByte = new byte[1024];
                int pdfSize = pdfResponseStream.Read(pdfByte, 0, pdfByte.Length);
                while (pdfSize > 0) {
                    pdfStream.Write(pdfByte, 0, pdfSize);
                    pdfSize = pdfResponseStream.Read(pdfByte, 0, pdfByte.Length);
                }
                pdfStream.Close();
                pdfResponseStream.Close();
                pdfAttachment.AttachmentName = pdfFileName;
                pdfAttachment.AttachmentExt = ".pdf";
                pdfAttachment.AttachmentLength = pdfByte.Length;
                pdfAttachment.AttachmentPath = pdfSavePath;
                pdfAttachment.UploadTime = DateTime.Now;
                service.InsertOne(pdfAttachment);
                console?.WriteLine("获取附件规划条件核实证明完成");
            }
        }


        /// <summary>
        /// get请求(xml结果)
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public static string HttpGet(string url) {
            //Encoding encoding = Encoding.UTF8;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "GET";
            request.Accept = "text/html, application/xhtml+xml, */*";
            request.ContentType = "application/xml";
            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8)) {
                return reader.ReadToEnd();
            }
        }
        ///<summary>
        ///生成随机验证码（数字字母）
        ///</summary>
        ///<param name="length">验证码的长度</param>
        ///<returns></returns>
        public static string CreateValidateNumber(int length) {
            //产生验证码的字符集
            string[] ValidateCharArray = { "2", "3", "4", "5", "6", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "J", "K", "M", "N", "P", "R", "S", "U", "W", "X", "Y", "a", "b", "c", "d", "e", "f", "g", "h", "j", "k", "m", "n", "p", "r", "s", "u", "w", "x", "y" };
            var sb = new StringBuilder();
            for (int i = 0; i < length; i++) {
                sb.Append(ValidateCharArray[Rand.Next(0, ValidateCharArray.Length)]);
            }
            return sb.ToString();
        }

        /// <summary>
        /// 检测MDB检查状态 - 用于征地拆迁数据交汇业务
        /// </summary>
        /// <param name="taskId">任务ID</param>
        /// <param name="businessID">业务ID</param>
        /// <param name="console">控制台</param>
        [DisplayName("检测MDB检查状态")]
        [AutomaticRetry(Attempts = 20, DelaysInSeconds = new[] { 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60 })]
        public static async Task LoadMdbCheckState(string taskId, string businessID, PerformContext console) {
            console?.WriteLine("开始检测MDB检查状态...");

            using (var service = new OracleDataService()) {
                var business = service.GetById<BusinessBaseInfo>(businessID);
                if (business?.BusinessClass != nameof(LandSurveyProjectFlow)) {
                    console?.WriteLine("业务类型不匹配，任务结束");
                    return;
                }

                List<LandSurvey_ContentInfo> projectList = service.GetList<LandSurvey_ContentInfo>("DataCheckID='" + taskId + "'");
                if (!projectList.Any()) {
                    console?.WriteLine("已不存在此TaskID，任务结束");
                    return;
                }

                var contentInfo = projectList.FirstOrDefault();

                try {
                    // 调用MDB状态检查接口
                    string statusUrl = $"{MdbCheckApiUrl}/mdb/status/{taskId}";
                    console?.WriteLine($"调用状态接口：{statusUrl}");

                    var httpClient = HttpClientFactory.Create();
                    var response = await httpClient.GetAsync(statusUrl);

                    if (!response.IsSuccessStatusCode) {
                        console?.WriteLine($"状态接口调用失败：{response.StatusCode}");
                        throw new Exception("状态接口调用失败，将重试");
                    }

                    var responseContent = await response.Content.ReadAsStringAsync();
                    console?.WriteLine($"状态接口返回：{responseContent}");

                    var statusResult = JsonConvert.DeserializeObject<JObject>(responseContent);
                    string status = statusResult["status"]?.ToString();

                    switch (status) {
                        case "pending":
                        case "running":
                            console?.WriteLine("检查仍在进行中，继续等待...");
                            throw new Exception("检查仍在进行中，将重试");

                        case "completed":
                            console?.WriteLine("检查完成，开始处理结果...");
                            await ProcessMdbCheckResult(taskId, contentInfo, service, console);
                            break;

                        case "failed":
                            console?.WriteLine("检查失败");
                            contentInfo.DataCheckState = 2; // 失败
                            contentInfo.RejectMessage = statusResult["message"]?.ToString() ?? "MDB检查失败";
                            service.UpdateOne(contentInfo);
                            break;

                        default:
                            console?.WriteLine($"未知状态：{status}");
                            throw new Exception($"未知状态：{status}，将重试");
                    }
                }
                catch (Exception ex) {
                    console?.WriteLine($"检查状态异常：{ex.Message}");
                    throw; // 重新抛出异常以触发重试
                }
            }
        }

        /// <summary>
        /// 处理MDB检查结果
        /// </summary>
        private static async Task ProcessMdbCheckResult(string taskId, LandSurvey_ContentInfo contentInfo, OracleDataService service, PerformContext console) {
            try {
                // 下载检查结果
                string downloadUrl = $"{MdbCheckApiUrl}/mdb/checkresult/download/{taskId}";
                console?.WriteLine($"下载检查结果：{downloadUrl}");

                var httpClient = HttpClientFactory.Create();
                var downloadResponse = await httpClient.GetAsync(downloadUrl);

                if (!downloadResponse.IsSuccessStatusCode) {
                    console?.WriteLine($"下载检查结果失败：{downloadResponse.StatusCode}");
                    contentInfo.DataCheckState = 2; // 失败
                    contentInfo.RejectMessage = "下载检查结果失败";
                    service.UpdateOne(contentInfo);
                    return;
                }

                var resultContent = await downloadResponse.Content.ReadAsStringAsync();
                console?.WriteLine($"检查结果：{resultContent}");

                var checkResult = JsonConvert.DeserializeObject<JObject>(resultContent);
                bool allPassed = true;
                var errorMessages = new List<string>();

                // 检查各个步骤的结果
                var steps = new[] { "basic_check", "feature_class_check", "topology_check", "database_check" };
                foreach (var step in steps) {
                    var stepResult = checkResult[step];
                    if (stepResult != null) {
                        bool stepPassed = stepResult["passed"]?.Value<bool>() ?? false;
                        if (!stepPassed) {
                            allPassed = false;
                            string stepMessage = stepResult["message"]?.ToString();
                            if (!string.IsNullOrEmpty(stepMessage)) {
                                errorMessages.Add($"{step}: {stepMessage}");
                            }
                        }
                    }
                }

                if (allPassed) {
                    console?.WriteLine("MDB检查通过");
                    contentInfo.DataCheckState = 1; // 通过
                    contentInfo.RejectMessage = null;

                    // 这里可以添加其他成功后的处理逻辑，比如提取测绘师信息等
                    // 暂时设置默认值
                    contentInfo.SurveyMasterName = "系统自动";
                    contentInfo.SurveyMasterNo = "AUTO";
                    contentInfo.SurveyMasterSureTime = DateTime.Now;
                } else {
                    console?.WriteLine("MDB检查未通过");
                    contentInfo.DataCheckState = 2; // 失败
                    contentInfo.RejectMessage = string.Join("；", errorMessages);
                }

                service.UpdateOne(contentInfo);
                console?.WriteLine($"更新检查状态完成：DataCheckState={contentInfo.DataCheckState}");
            }
            catch (Exception ex) {
                console?.WriteLine($"处理检查结果异常：{ex.Message}");
                contentInfo.DataCheckState = 2; // 失败
                contentInfo.RejectMessage = $"处理检查结果异常：{ex.Message}";
                service.UpdateOne(contentInfo);
            }
        }
    }
}