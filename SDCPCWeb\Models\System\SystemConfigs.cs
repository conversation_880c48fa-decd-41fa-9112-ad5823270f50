﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Configuration;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.CompanyRegister;
using SDCPCWeb.Services;
using SDCPCWeb.ViewModels;

namespace SDCPCWeb.Models.System {
    /// <summary>
    /// 工作流配置
    /// </summary>
    public static class BusinessFlowConfig {
        /// <summary>
        /// 基础数据下载流程
        /// </summary>
        public static BaseSurveyDataDownloadFlow BaseSurveyDataDownloadFlow { get; set; }
        /// <summary>
        /// 放线测量流程
        /// </summary>
        public static MeasurePutLineFlow MeasurePutLineFlow { get; set; }
        /// <summary>
        /// 不动产预测绘流程
        /// </summary>
        public static RealEstatePreSurveyFlow RealEstatePreSurveyFlow { get; set; }
        /// <summary>
        /// 蓝线图流程
        /// </summary>
        public static BlueLineSurveyFlow BlueLineSurveyFlow { get; set; }
        /// <summary>
        /// 规划定点流程
        /// </summary>
        public static MarkPointSurveyFlow MarkPointSurveyFlow { get; set; }
        ///// <summary>
        ///// 规划核实测量流程
        ///// </summary>
        //public static PlanCheckSurveyFlow PlanCheckSurveyFlow { get; set; }
        /// <summary>
        /// 不动产实测绘流程
        /// </summary>
        public static RealEstateActualSurveyFlow RealEstateActualSurveyFlow { get; set; }
        /// <summary>
        /// 不动产预核测绘流程
        /// </summary>
        public static RealEstatePreCheckSurveyFlow RealEstatePreCheckSurveyFlow { get; set; }

        /// <summary>
        /// 不动产预测绘楼盘表变更流程
        /// </summary>
        public static RealEstatePreSurveyBuildingTableChangeFlow RealEstatePreSurveyBuildingTableChangeFlow { get; set; }

        /// <summary>
        /// 不动产预测绘成果变更流程
        /// </summary>
        public static RealEstatePreSurveyResultChangeFlow RealEstatePreSurveyResultChangeFlow { get; set; }

        /// <summary>
        /// 不动产实测绘楼盘表变更流程
        /// </summary>
        public static RealEstateActualBuildingTableChangeFlow RealEstateActualBuildingTableChangeFlow { get; set; }

        /// <summary>
        /// 不动产实测绘成果变更流程
        /// </summary>
        public static RealEstateActualResultChangeFlow RealEstateActualResultChangeFlow { get; set; }

        /// <summary>
        /// 不动产放线测量与规划验线（非房开企业）流程
        /// </summary>
        public static MeasurePutLinePreCheckFlow MeasurePutLinePreCheckFlow { get; set; }

        /// <summary>
        /// 不动产预核测绘自动办理流程
        /// </summary>
        public static RealEstatePreCheckSurveyAutoFlow RealEstatePreCheckSurveyAutoFlow { get; set; }

        /// <summary>
        /// 市政工程建设竣工规划核实业务流程
        /// </summary>
        public static CouncilPlanCheckFlow CouncilPlanCheckFlow { get; set; }

        /// <summary>
        /// 市政工程放线测量与规划验线（非房开企业）流程
        /// </summary>
        public static CouncilMeasurePutLinePreCheckFlow CouncilMeasurePutLinePreCheckFlow { get; set; }

        /// <summary>
        /// 不动产全面核实业务流程
        /// </summary>
        public static RealEstateOverallActualSurveyFlow RealEstateOverallActualSurveyFlow { get; set; }

        /// <summary>
        /// 征地拆迁数据交汇业务流程
        /// </summary>
        public static LandSurveyProjectFlow LandSurveyProjectFlow { get; set; }

        /// <summary>
        /// 当前已启用流程
        /// </summary>
        public static List<string> EnableFlows { get; set; }

        /// <summary>
        /// 当前可见流程
        /// </summary>
        public static List<string> VisibleFlows { get; set; }

        /// <summary>
        /// 云平台步骤列表
        /// </summary>
        public static List<XCloudStepBusinessInfoViewModel> XCloudStepBusinessList { get; set; }

        //如果有新的流程模型定义，要在这里新增静态属性

        /// <summary>
        /// 初始化流程定义
        /// </summary>
        public static void Initialize() {
            BaseSurveyDataDownloadFlow = new BaseSurveyDataDownloadFlow();
            MeasurePutLineFlow = new MeasurePutLineFlow();
            RealEstatePreSurveyFlow = new RealEstatePreSurveyFlow();
            BlueLineSurveyFlow = new BlueLineSurveyFlow();
            MarkPointSurveyFlow = new MarkPointSurveyFlow();
            //PlanCheckSurveyFlow = new PlanCheckSurveyFlow();
            RealEstateActualSurveyFlow = new RealEstateActualSurveyFlow();
            RealEstatePreCheckSurveyFlow = new RealEstatePreCheckSurveyFlow();
            RealEstatePreSurveyBuildingTableChangeFlow = new RealEstatePreSurveyBuildingTableChangeFlow();
            RealEstatePreSurveyResultChangeFlow = new RealEstatePreSurveyResultChangeFlow();
            RealEstateActualBuildingTableChangeFlow = new RealEstateActualBuildingTableChangeFlow();
            RealEstateActualResultChangeFlow = new RealEstateActualResultChangeFlow();
            MeasurePutLinePreCheckFlow = new MeasurePutLinePreCheckFlow();
            RealEstatePreCheckSurveyAutoFlow = new RealEstatePreCheckSurveyAutoFlow();
            CouncilPlanCheckFlow = new CouncilPlanCheckFlow();
            CouncilMeasurePutLinePreCheckFlow = new CouncilMeasurePutLinePreCheckFlow();
            RealEstateOverallActualSurveyFlow = new RealEstateOverallActualSurveyFlow();
            LandSurveyProjectFlow = new LandSurveyProjectFlow();
            //如果有新的流程模型定义，要在这里新增赋值

            //当前可用流程配置
            EnableFlows = WebConfigurationManager.AppSettings["EnableFlows"]?.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries).ToList() ?? new List<string>();
            //当前可见流程
            VisibleFlows = WebConfigurationManager.AppSettings["VisibleFlows"]?.Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries).ToList() ?? new List<string>();

            //云平台步骤信息json文件路径
            string xCloudStepFilePath = WebConfigurationManager.AppSettings["XCloudStepFilePath"];

            //获取云平台步骤信息
            XCloudStepBusinessList = GetXCloudStepList(xCloudStepFilePath);
        }

        /// <summary>
        /// 所有支持的流程
        /// </summary>
        public static List<BusinessFlowBase> AllFlows => new List<BusinessFlowBase>() {
            MeasurePutLineFlow,
            RealEstatePreSurveyFlow,
            BlueLineSurveyFlow,
            MarkPointSurveyFlow,
            RealEstateActualSurveyFlow,
            RealEstatePreCheckSurveyFlow,
            RealEstatePreSurveyBuildingTableChangeFlow,
            RealEstatePreSurveyResultChangeFlow,
            RealEstateActualBuildingTableChangeFlow,
            RealEstateActualResultChangeFlow,
            MeasurePutLinePreCheckFlow,
            RealEstatePreCheckSurveyAutoFlow,
            CouncilPlanCheckFlow,
            CouncilMeasurePutLinePreCheckFlow,
            RealEstateOverallActualSurveyFlow,
            LandSurveyProjectFlow
        };

        /// <summary>
        /// 获取流程
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public static T GetFlow<T>() where T : BusinessFlowBase {
            if (typeof(T) == typeof(BaseSurveyDataDownloadFlow)) {
                return BaseSurveyDataDownloadFlow as T;
            }
            if (typeof(T) == typeof(MeasurePutLineFlow)) {
                return MeasurePutLineFlow as T;
            }
            if (typeof(T) == typeof(RealEstatePreSurveyFlow)) {
                return RealEstatePreSurveyFlow as T;
            }
            if (typeof(T) == typeof(BlueLineSurveyFlow)) {
                return BlueLineSurveyFlow as T;
            }
            if (typeof(T) == typeof(MarkPointSurveyFlow)) {
                return MarkPointSurveyFlow as T;
            }
            //if (typeof(T) == typeof(PlanCheckSurveyFlow)) {
            //    return PlanCheckSurveyFlow as T;
            //}
            if (typeof(T) == typeof(RealEstateActualSurveyFlow)) {
                return RealEstateActualSurveyFlow as T;
            }
            if (typeof(T) == typeof(RealEstatePreCheckSurveyFlow)) {
                return RealEstatePreCheckSurveyFlow as T;
            }
            if (typeof(T) == typeof(RealEstatePreSurveyBuildingTableChangeFlow)) {
                return RealEstatePreSurveyBuildingTableChangeFlow as T;
            }
            if (typeof(T) == typeof(RealEstatePreSurveyResultChangeFlow)) {
                return RealEstatePreSurveyResultChangeFlow as T;
            }
            if (typeof(T) == typeof(RealEstateActualBuildingTableChangeFlow)) {
                return RealEstateActualBuildingTableChangeFlow as T;
            }
            if (typeof(T) == typeof(RealEstateActualResultChangeFlow)) {
                return RealEstateActualResultChangeFlow as T;
            }
            if (typeof(T) == typeof(MeasurePutLinePreCheckFlow)) {
                return MeasurePutLinePreCheckFlow as T;
            }
            if (typeof(T) == typeof(RealEstatePreCheckSurveyAutoFlow)) {
                return RealEstatePreCheckSurveyAutoFlow as T;
            }
            if (typeof(T) == typeof(CouncilPlanCheckFlow)) {
                return CouncilPlanCheckFlow as T;
            }
            if (typeof(T) == typeof(CouncilMeasurePutLinePreCheckFlow)) {
                return CouncilMeasurePutLinePreCheckFlow as T;
            }
            if (typeof(T) == typeof(RealEstateOverallActualSurveyFlow)) {
                return RealEstateOverallActualSurveyFlow as T;
            }
            if (typeof(T) == typeof(LandSurveyProjectFlow)) {
                return LandSurveyProjectFlow as T;
            }
            //todo 如果有新的流程模型定义，要在这里新增返回
            return default(T);
        }

        public static BusinessFlowBase GetFlow(Type type) {
            if (type == typeof(BaseSurveyDataDownloadFlow)) {
                return BaseSurveyDataDownloadFlow;
            }
            if (type == typeof(MeasurePutLineFlow)) {
                return MeasurePutLineFlow;
            }
            if (type == typeof(RealEstatePreSurveyFlow)) {
                return RealEstatePreSurveyFlow;
            }
            if (type == typeof(BlueLineSurveyFlow)) {
                return BlueLineSurveyFlow;
            }
            if (type == typeof(MarkPointSurveyFlow)) {
                return MarkPointSurveyFlow;
            }
            //if (type == typeof(PlanCheckSurveyFlow)) {
            //    return PlanCheckSurveyFlow;
            //}
            if (type == typeof(RealEstateActualSurveyFlow)) {
                return RealEstateActualSurveyFlow;
            }
            if (type == typeof(RealEstatePreCheckSurveyFlow)) {
                return RealEstatePreCheckSurveyFlow;
            }
            if (type == typeof(RealEstatePreSurveyBuildingTableChangeFlow)) {
                return RealEstatePreSurveyBuildingTableChangeFlow;
            }
            if (type == typeof(RealEstatePreSurveyResultChangeFlow)) {
                return RealEstatePreSurveyResultChangeFlow;
            }
            if (type == typeof(RealEstateActualBuildingTableChangeFlow)) {
                return RealEstateActualBuildingTableChangeFlow;
            }
            if (type == typeof(RealEstateActualResultChangeFlow)) {
                return RealEstateActualResultChangeFlow;
            }
            if (type == typeof(MeasurePutLinePreCheckFlow)) {
                return MeasurePutLinePreCheckFlow;
            }
            if (type == typeof(RealEstatePreCheckSurveyAutoFlow)) {
                return RealEstatePreCheckSurveyAutoFlow;
            }
            if (type == typeof(CouncilPlanCheckFlow)) {
                return CouncilPlanCheckFlow;
            }
            if (type == typeof(CouncilMeasurePutLinePreCheckFlow)) {
                return CouncilMeasurePutLinePreCheckFlow;
            }
            if (type == typeof(RealEstateOverallActualSurveyFlow)) {
                return RealEstateOverallActualSurveyFlow;
            }
            if (type == typeof(LandSurveyProjectFlow)) {
                return LandSurveyProjectFlow;
            }
            //todo 如果有新的流程模型定义，要在这里新增返回
            return null;
        }

        /// <summary>
        /// 判断用户是否有权限创建此业务
        /// </summary>
        /// <param name="userid"></param>
        /// <param name="personNo"></param>
        /// <param name="role"></param>
        /// <param name="business"></param>
        /// <returns></returns>
        public static bool CheckRole(string userid, string personNo, UserRole role, BusinessBaseInfoModel business) {
            switch (role) {
                case UserRole.BusinessNormal:
                    return CheckUserIsBusinessNormal(personNo);
                case UserRole.BusinessAdmin:
                    return CheckUserIsBusinessAdmin(personNo);
                case UserRole.SurveyNormal:
                    return CheckUserIsSurveyNormal(personNo);
                case UserRole.SurveyAdmin:
                    return CheckUserIsSurveyAdmin(personNo);
                case UserRole.SurveyMaster:
                    return CheckUserIsSurveyMaster(personNo);
                case UserRole.AuditorOfSurvey:
                    return new[] { "450821198707135810" }.Contains(personNo);
                case UserRole.AuditorOfBusiness:
                    return new[] { "450821198707135810" }.Contains(personNo);
                case UserRole.DataManager:
                    return new[] { "450821198707135810" }.Contains(personNo);
                case UserRole.LastAuditor:
                    return new[] { "450821198707135810" }.Contains(personNo);
                case UserRole.Myself:
                    return true;
                case UserRole.ProjectCreator:
                default:
                    return CheckUserIsProjectCreator(userid, business);
            }
        }

        private static bool CheckUserIsSurveyMaster(string personNo) {
            using (var service = new OracleDataService()) {
                var members = service.GetList<CompanyEmployees>(
                    $"PersonNumber='{personNo}' AND PersonRole='注册测绘师' AND RelationRequestId IN(SELECT RelationRequestId FROM CompanyBaseInfo WHERE CompanyType='测绘单位')");
                return members.Any();
            }
        }

        private static bool CheckUserIsSurveyAdmin(string personNo) {
            using (var service = new OracleDataService()) {
                var members = service.GetList<CompanyEmployees>(
                    $"PersonNumber='{personNo}' AND PersonRole='单位管理员' AND RelationRequestId IN(SELECT RelationRequestId FROM CompanyBaseInfo WHERE CompanyType='测绘单位')");
                return members.Any();
            }
        }

        private static bool CheckUserIsSurveyNormal(string personNo) {
            using (var service = new OracleDataService()) {
                var members = service.GetList<CompanyEmployees>(
                    $"PersonNumber='{personNo}' AND PersonRole='测绘人员' AND RelationRequestId IN(SELECT RelationRequestId FROM CompanyBaseInfo WHERE CompanyType='测绘单位')");
                return members.Any();
            }
        }

        /// <summary>
        /// 判断用户是否为建设单位的单位管理员
        /// </summary>
        /// <param name="personNo"></param>
        /// <returns></returns>
        private static bool CheckUserIsBusinessAdmin(string personNo) {
            using (var service = new OracleDataService()) {
                var members = service.GetList<DeveloperEmployee>(
                    $"PersonNumber='{personNo}' AND PersonRole='单位管理员' AND RelationRequestId IN(SELECT RelationRequestId FROM CompanyBaseInfo WHERE CompanyType!='测绘单位')");
                return members.Any();
            }
        }

        /// <summary>
        /// 判断用户是否为建设单位一般业务员
        /// </summary>
        /// <param name="personNo"></param>
        /// <returns></returns>
        private static bool CheckUserIsBusinessNormal(string personNo) {
            using (var service = new OracleDataService()) {
                var members = service.GetList<DeveloperEmployee>(
                    $"PersonNumber='{personNo}' AND PersonRole='报建员' AND RelationRequestId IN(SELECT RelationRequestId FROM CompanyBaseInfo WHERE CompanyType!='测绘单位')");
                return members.Any();
            }
        }

        /// <summary>
        /// 判断用户是否为
        /// </summary>
        /// <param name="userid"></param>
        /// <param name="business"></param>
        /// <returns></returns>
        private static bool CheckUserIsProjectCreator(string userid, BusinessBaseInfoModel business) {
            if (string.IsNullOrWhiteSpace(userid)) return false;
            if (string.IsNullOrWhiteSpace(business?.CreateUserId)) return false;
            return business.CreateUserId == userid;
        }

        /// <summary>
        /// 读取json文件到步骤列表
        /// </summary>
        /// <returns></returns>
        private static List<XCloudStepBusinessInfoViewModel> GetXCloudStepList(string path) {
            Assembly assembly = Assembly.GetExecutingAssembly();
            string filePath = assembly.GetName().Name + path;
            Stream stream = assembly.GetManifestResourceStream(filePath);
            StreamReader sr = new StreamReader(stream, Encoding.UTF8);

            string json = sr.ReadToEnd();
            return JsonConvert.DeserializeObject<List<XCloudStepBusinessInfoViewModel>>(json);
        }

        /// <summary>
        /// 生成云平台步骤
        /// </summary>
        /// <param name="id">业务id</param>
        /// <param name="businessClass">业务类型</param>
        /// <returns></returns>
        public static async Task<JObject> CreateXCloudSteps(string id, string businessClass) {
            var spjl = await XCloudService.GetFlowStepRecordsByBusinessId(id, businessClass); //获取云平台审批记录
            if (!string.IsNullOrWhiteSpace(spjl.Item1))
                return JObject.FromObject(new { Message = spjl.Item1 });

            var newXCloudStepList =
                JsonConvert.DeserializeObject<List<XCloudStepInfoViewModel>>(
                    JsonConvert.SerializeObject(
                        XCloudStepBusinessList.Where(s => s.BusinessClass == businessClass)
                            .FirstOrDefault()?.Steps
                    ));
            if (spjl.Item2.Any()) {
                foreach (var item in spjl.Item2) {
                    var step = newXCloudStepList.FirstOrDefault(s => s.StepNames.Contains(item["CurActionName"].ToString()));
                    if (step != null) {
                        step.IsCurrent = true;

                        //如果最后一个步骤是结束
                        if (item["CurActionName"].ToString() == "结束") {
                            step.IsFinished = true;
                        }

                        //把比它小的步骤设置为已完成
                        foreach (var xCloudStepViewModel in newXCloudStepList.Where(s => s.Index < step.Index)) {
                            xCloudStepViewModel.IsFinished = true;
                        }
                        break;
                    }
                }
            }

            return JObject.FromObject(new { Message = "", Data = newXCloudStepList.Select(s => new { s.DisplayName, s.IsCurrent, s.IsFinished }) });
        }

        /// <summary>
        /// 获取业务列表
        /// </summary>
        /// <returns></returns>
        public static List<object> GetFlowList() {
            var data = new List<object>();
            var catalog1 = new {
                value = 0,
                label = "项目立项前 - 测绘数据申请",
                icon = "dataitem",
                children = new List<object>()
            };
            catalog1.children.Add(new {
                value = 11,
                label = BaseSurveyDataDownloadFlow.FlowName,
                businessClass = BaseSurveyDataDownloadFlow.GetType().Name,
                url = "/user/business/base-data",
                children = default(object),
                enable = BaseSurveyDataDownloadFlow.Enable,
                visible = BaseSurveyDataDownloadFlow.Visible
            });
            data.Add(catalog1);
            #region
            //暂时去掉勘测定界
            //var catalog2 = new {
            //    value = 1,
            //    label = "勘测定界",
            //    icon = "ditu",
            //    children = new List<object>()
            //};
            //catalog2.children.Add(new {
            //    value = 6,
            //    label = "蓝线图",
            //    businessClass = "BlueLineSurveyFlow",
            //    url = "/user/business/blue-line",
            //    children = default(object),
            //    enable = EnableFlows.Contains("BlueLineSurveyFlow")
            //});
            //data.Add(catalog2);
            #endregion
            var catalog3 = new {
                value = 2,
                label = "项目立项阶段 - 规划定点及不动产权籍调查",
                icon = "area",
                children = new List<object>()
            };
            catalog3.children.Add(new {
                value = 7,
                label = MarkPointSurveyFlow.FlowName,
                businessClass = MarkPointSurveyFlow.GetType().Name,
                url = "/user/business/staking",
                children = default(object),
                enable = MarkPointSurveyFlow.Enable,
                visible = MarkPointSurveyFlow.Visible
            });
            data.Add(catalog3);
            var catalog4 = new {
                value = 3,
                label = "项目许可阶段 - 规划验线",
                icon = "xiantuceng",
                children = new List<object>()
            };
            catalog4.children.Add(new {
                value = 8,
                label = MeasurePutLineFlow.FlowName,
                businessClass = MeasurePutLineFlow.GetType().Name,
                url = "/user/business/setout-survey",
                children = default(object),
                enable = MeasurePutLineFlow.Enable,
                visible = MeasurePutLineFlow.Visible
            });
            catalog4.children.Add(new {
                value = 9,
                label = RealEstatePreSurveyFlow.FlowName,
                businessClass = RealEstatePreSurveyFlow.GetType().Name,
                url = "/user/business/pre-mapping",
                children = default(object),
                enable = RealEstatePreSurveyFlow.Enable,
                visible = RealEstatePreSurveyFlow.Visible
            });
            catalog4.children.Add(new {
                value = 11,
                label = RealEstatePreCheckSurveyFlow.FlowName,
                businessClass = RealEstatePreCheckSurveyFlow.GetType().Name,
                url = "/user/business/pre-mapping-merge",
                children = default(object),
                enable = RealEstatePreCheckSurveyFlow.Enable,
                visible = RealEstatePreCheckSurveyFlow.Visible
            });
            //不动产预核业务楼盘表变更业务
            catalog4.children.Add(new {
                value = 12,
                label = RealEstatePreSurveyBuildingTableChangeFlow.FlowName,
                businessClass = RealEstatePreSurveyBuildingTableChangeFlow.GetType().Name,
                url = "/user/business/change/pre-merge-building",
                children = default(object),
                enable = RealEstatePreSurveyBuildingTableChangeFlow.Enable,
                visible = RealEstatePreSurveyBuildingTableChangeFlow.Visible
            });
            //不动产预核业务成果变更业务
            catalog4.children.Add(new {
                value = 13,
                label = RealEstatePreSurveyResultChangeFlow.FlowName,
                businessClass = RealEstatePreSurveyResultChangeFlow.GetType().Name,
                url = "/user/business/change/pre-merge-result",
                children = default(object),
                enable = RealEstatePreSurveyResultChangeFlow.Enable,
                visible = RealEstatePreSurveyResultChangeFlow.Visible
            });
            //不动产预核业务单独验线业务
            catalog4.children.Add(new {
                value = 16,
                label = MeasurePutLinePreCheckFlow.FlowName,
                businessClass = MeasurePutLinePreCheckFlow.GetType().Name,
                url = "/user/business/planning-check",
                children = default(object),
                enable = MeasurePutLinePreCheckFlow.Enable,
                visible = MeasurePutLinePreCheckFlow.Visible
            });
            //不动产预核业务即时办理流程
            catalog4.children.Add(new {
                value = 11,
                label = RealEstatePreCheckSurveyAutoFlow.FlowName,
                businessClass = RealEstatePreCheckSurveyAutoFlow.GetType().Name,
                url = "/user/business/pre-mapping-merge-auto",
                children = default(object),
                enable = RealEstatePreCheckSurveyAutoFlow.Enable,
                visible = RealEstatePreCheckSurveyAutoFlow.Visible
            });
            //市政工程预核业务单独验线业务
            catalog4.children.Add(new {
                value = 19,
                label = CouncilMeasurePutLinePreCheckFlow.FlowName,
                businessClass = CouncilMeasurePutLinePreCheckFlow.GetType().Name,
                url = "/user/business/council-planning-check",
                children = default(object),
                enable = CouncilMeasurePutLinePreCheckFlow.Enable,
                visible = CouncilMeasurePutLinePreCheckFlow.Visible
            });
            data.Add(catalog4);
            var catalog5 = new {
                value = 4,
                label = "工程竣工验收阶段 - 不动产规划核实及实测",
                icon = "shenhexiangmu",
                children = new List<object>()
            };
            //catalog5.children.Add(new {
            //    value = 10,
            //    label = "规划条件核实测量",
            //    businessClass = "PlanCheckSurveyFlow",
            //    url = "/user/business/proj-measure",
            //    children = default(object),
            //    enable = EnableFlows.Contains("PlanCheckSurveyFlow")
            //});
            catalog5.children.Add(new {
                value = 10,
                label = RealEstateActualSurveyFlow.FlowName,
                businessClass = RealEstateActualSurveyFlow.GetType().Name,
                url = "/user/business/actual-mapping",
                children = default(object),
                enable = RealEstateActualSurveyFlow.Enable,
                visible = RealEstateActualSurveyFlow.Visible
            });
            catalog5.children.Add(new {
                value = 14,
                label = RealEstateActualBuildingTableChangeFlow.FlowName,
                businessClass = RealEstateActualBuildingTableChangeFlow.GetType().Name,
                url = "/user/business/change/actual-mapping-building",
                children = default(object),
                enable = RealEstateActualBuildingTableChangeFlow.Enable,
                visible = RealEstateActualBuildingTableChangeFlow.Visible
            });
            catalog5.children.Add(new {
                value = 15,
                label = RealEstateActualResultChangeFlow.FlowName,
                businessClass = RealEstateActualResultChangeFlow.GetType().Name,
                url = "/user/business/change/actual-mapping-result",
                children = default(object),
                enable = RealEstateActualResultChangeFlow.Enable,
                visible = RealEstateActualResultChangeFlow.Visible
            });
            catalog5.children.Add(new {
                value = 18,
                label = CouncilPlanCheckFlow.FlowName,
                businessClass = CouncilPlanCheckFlow.GetType().Name,
                url = "/user/business/council-plan-check",
                children = default(object),
                enable = CouncilPlanCheckFlow.Enable,
                visible = CouncilPlanCheckFlow.Visible
            });
            catalog5.children.Add(new {
                value = 20,
                label = RealEstateOverallActualSurveyFlow.FlowName,
                businessClass = RealEstateOverallActualSurveyFlow.GetType().Name,
                url = "/user/business/overall-actual-mapping",
                children = default(object),
                enable = RealEstateOverallActualSurveyFlow.Enable,
                visible = RealEstateOverallActualSurveyFlow.Visible
            });
            data.Add(catalog5);
            var catalog6 = new {
                value = 5,
                label = "征地拆迁 - 测绘数据汇交",
                icon = "yewu1",
                children = new List<object>()
            };
            catalog6.children.Add(new {
                value = 21,
                label = LandSurveyProjectFlow.FlowName,
                businessClass = LandSurveyProjectFlow.GetType().Name,
                url = "/user/business/land-survey-mapping",
                children = default(object),
                enable = LandSurveyProjectFlow.Enable,
                visible = LandSurveyProjectFlow.Visible
            });
            data.Add(catalog6);
            return data;
        }
    }

    /// <summary>
    /// 外部API配置
    /// </summary>
    public static class ExternalApiConfig {
        /// <summary>
        /// 单点登录站点
        /// </summary>
        public static string LoginServer = WebConfigurationManager.AppSettings["LoginServer"];
        /// <summary>
        /// 附件根目录
        /// </summary>
        public static string RootPath = WebConfigurationManager.AppSettings["RootPath"];
        /// <summary>
        /// 工程规划许可接口
        /// </summary>
        public static string ServiceUrl = WebConfigurationManager.AppSettings["ServiceUrl"];
        /// <summary>
        /// 工程规划许可接口1
        /// </summary>
        public static string ServiceUrl1 = WebConfigurationManager.AppSettings["ServiceUrl1"];
        /// <summary>
        /// 市政工程规划许可接口地址
        /// </summary>
        public static string SZServiceUrl = WebConfigurationManager.AppSettings["SZServiceUrl"];
        /// <summary>
        /// 云平台接口
        /// </summary>
        public static string xCloudApiUrl = WebConfigurationManager.AppSettings["xCloudApiUrl"];
        /// <summary>
        /// 测绘子系统-云平台接口地址
        /// </summary>
        public static string CH_xCloudApiUrl = WebConfigurationManager.AppSettings["CH_xCloudApiUrl"];
        /// <summary>
        /// EPS WebService接口
        /// </summary>
        public static string EPSSDCServices = WebConfigurationManager.AppSettings["EPSSDCServices"];
        /// <summary>
        /// EPS 成果下载接口
        /// </summary>
        public static string EPSDownUrl = WebConfigurationManager.AppSettings["EPSDownUrl"];
        /// <summary>
        /// 短信服务接口
        /// </summary>
        public static string CloudSmsServiceUrl = WebConfigurationManager.AppSettings["CloudSmsServiceUrl"];
        /// <summary>
        /// 系统共享查询接口
        /// </summary>
        public static string BDCSystemApiUrl = WebConfigurationManager.AppSettings["BDCSystemApiUrl"];
        /// <summary>
        /// 邕e登DataApi接口
        /// </summary>
        public static string DataApiUrl = WebConfigurationManager.AppSettings["DataApiUrl"];
        /// <summary>
        /// 综合平台外网接口
        /// </summary>
        public static string BDCWebServiceUrl = WebConfigurationManager.AppSettings["BDCWebServiceUrl"];
        /// <summary>
        /// EPS上传全面核实MDB检查接口
        /// </summary>
        public static string EPSOverallUploadMDBUrl = WebConfigurationManager.AppSettings["EPSOverallUploadMDBUrl"];
    }

    /// <summary>
    /// 系统运行配置
    /// </summary>
    public static class SystemConfig {
        /// <summary>
        /// 私钥文件
        /// </summary>
        public static string PrivateKeyFile = WebConfigurationManager.AppSettings["PrivateKeyFile"];
        /// <summary>
        /// 当前自己身份标识
        /// </summary>
        public static string LocalIdentity = WebConfigurationManager.AppSettings["LocalIdentity"];
        /// <summary>
        /// 系统固定的设置
        /// </summary>
        public static JsonConverter JsonDateTimeConverter = new IsoDateTimeConverter { DateTimeFormat = "yyyy-MM-dd HH:mm:ss" };
    }

    /// <summary>
    /// 用户角色
    /// </summary>
    public enum UserRole {
        /// <summary>
        /// 报建员
        /// </summary>
        BusinessNormal,
        /// <summary>
        /// 业主单位管理员
        /// </summary>
        BusinessAdmin,
        /// <summary>
        /// 测绘人员
        /// </summary>
        SurveyNormal,
        /// <summary>
        /// 测绘单位管理员
        /// </summary>
        SurveyAdmin,
        /// <summary>
        /// 注册测绘师
        /// </summary>
        SurveyMaster,
        /// <summary>
        /// 测绘单位审核人
        /// </summary>
        AuditorOfSurvey,
        /// <summary>
        /// 建设单位审核人
        /// </summary>
        AuditorOfBusiness,
        /// <summary>
        /// 数据管理员
        /// </summary>
        DataManager,
        /// <summary>
        /// 业务创建者
        /// </summary>
        ProjectCreator,
        /// <summary>
        /// 上次审核人
        /// </summary>
        LastAuditor,
        /// <summary>
        /// 当前用户
        /// </summary>
        Myself,
    }

    /// <summary>
    /// 环节提交验证的方法代理
    /// </summary>
    /// <returns></returns>
    public delegate bool ValidateFlowActionPostHandler(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel linkInfo);

    /// <summary>
    /// 环节提交事件的方法代理
    /// </summary>
    public delegate void FlowActionPostHandler(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel linkInfo);

}